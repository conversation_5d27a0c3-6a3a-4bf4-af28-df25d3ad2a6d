package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.NonNull;

import java.util.List;

public record OfferingState(

        int tenderId,

        @NonNull
        List<OfferingStep> steps

) {

    public OfferingStep getStepOfType(OfferingStepType type) {
        return steps.stream()
                .filter(offeringStep -> offeringStep.type() == type)
                .findFirst()
                .orElseThrow(() -> new ItemNotFoundException(OfferingStepType.class, type));
    }
}
