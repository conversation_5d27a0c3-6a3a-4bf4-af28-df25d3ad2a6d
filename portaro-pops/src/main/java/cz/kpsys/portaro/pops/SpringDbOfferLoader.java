package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.pops.PopsDb.POPS_NABIDKY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbOfferLoader implements OfferLoader, RowMapper<OfferEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Converter<List<? extends OfferEntity>, List<Offer>> entitiesToOffersConverter;


    @Override
    public Offer getById(@NonNull Integer id) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where().eq(ID_NABIDKY, id);
        OfferEntity entity = DataUtils.requireSingle(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this), Offer.class, id);
        return ListUtil.convertSingle(entity, entitiesToOffersConverter);
    }


    @Override
    public List<? extends Offer> getAllByTenderAndSupplierCompany(Tender tender, @Nullable UUID supplierAuthorityId, boolean includeDeleted) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where().startsWith(FK_POPTAVKY, tender.getId() + "_");
        if (supplierAuthorityId != null) {
            sq.where().and().eq(SUPPLIER_RECORD_ID, supplierAuthorityId);
        }
        if (!includeDeleted) {
            sq.where().and().isNull(CAS_SMAZANI);
        }
        List<OfferEntity> entities = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
        return entitiesToOffersConverter.convert(entities);
    }


    @Override
    public List<? extends Offer> getAllByDemands(List<String> demandIds) {
        return getAllByDemandsAndSupplierCompany(demandIds, null);
    }


    public List<? extends Offer> getAllByDemandsAndSupplierCompany(List<String> demandIds, @Nullable UUID supplierAuthorityId) {
        if (ListUtil.isNullOrEmpty(demandIds)) {
            return List.of();
        }

        return DataUtils.loadInChunksWithSortingById(demandIds, 100, demandIdsBatch -> partialQueryAllByDemandsAndSupplierCompany(demandIdsBatch, supplierAuthorityId), Offer::getDemandId);
    }


    private List<? extends Offer> partialQueryAllByDemandsAndSupplierCompany(List<String> demandIds, @Nullable UUID supplierAuthorityId) {
        if (demandIds.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where()
                .in(FK_POPTAVKY, demandIds)
                .and()
                .isNull(CAS_SMAZANI);
        if (supplierAuthorityId != null) {
            sq.where().and().eq(SUPPLIER_RECORD_ID, supplierAuthorityId);
        }
        List<OfferEntity> entities = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
        List<Offer> offers = entitiesToOffersConverter.convert(entities);

        return ListUtil.filterAndSortByRule(offers, demandIds, Offer::getDemandId);
    }


    @Override
    public OfferEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        @NonNull Integer id = DbUtils.getIntegerNotNull(rs, ID_NABIDKY);
        @NonNull String demandId = DbUtils.getStringNotNull(rs, FK_POPTAVKY);
        @NonNull Integer representerUserId = DbUtils.getIntegerNotNull(rs, FK_UZIV);
        @NonNull UUID supplierRecordId = DbUtils.uuidNotNull(rs, SUPPLIER_RECORD_ID);
        @NonNull Integer quantity = DbUtils.getIntegerNotNull(rs, POC_DODATELNYCH);
        @NonNull BigDecimal priceAmount = DbUtils.bigDecimalNotNull(rs, CENA_JEDN);
        @NonNull Instant creationDate = DbUtils.instantNotNull(rs, CAS);
        Instant deleteDate = DbUtils.instantOrNull(rs, CAS_SMAZANI);
        String note = rs.getString(POZNAMKA);
        Integer deletedByUserId = DbUtils.getInteger(rs, FK_UZIV_SMAZ);

        return new OfferEntity(
                id,
                demandId,
                representerUserId,
                supplierRecordId,
                quantity,
                priceAmount,
                creationDate,
                deletedByUserId,
                deleteDate,
                note
        );
    }

}
