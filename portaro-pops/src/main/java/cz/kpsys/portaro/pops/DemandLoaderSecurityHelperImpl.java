package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.Institution;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DemandLoaderSecurityHelperImpl implements DemandLoaderSecurityHelper {

    @NonNull SecurityManager securityManager;
    @NonNull DemandLoader demandLoader;

    @Override
    public List<Demand> getAllByTenderOptionallyWithAllOffers(Department currentDepartment, UserAuthentication currentAuth, int tenderId) {
        if (securityManager.can(PopsSecurityActions.POPS_ALL_OFFERS_SHOW, currentAuth, currentDepartment, null)) {
            return demandLoader.getAllWithAllOffers(tenderId);
        }

        //ostatni typy uzivatelu nevidi zadne nabidky
        return demandLoader.getAllByTenderWithoutOffers(tenderId);
    }

    @Override
    public List<Demand> getAllByTenderOptionallyWithSupplierOffers(Department currentDepartment, UserAuthentication currentAuth, int tenderId, Institution supplierCompany) {
        if (securityManager.can(PopsSecurityActions.POPS_SUPPLIER_OFFERS_SHOW, currentAuth, currentDepartment, supplierCompany)) {
            return demandLoader.getAllByTenderWithSupplierOffers(tenderId, supplierCompany.getRecordId());
        }

        //ostatni typy uzivatelu nevidi zadne nabidky
        return demandLoader.getAllByTenderWithoutOffers(tenderId);
    }
}
