package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import static cz.kpsys.portaro.pops.PopsDb.POPS_POPTAVKY.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbDemandLoader implements DemandLoader, RowMapper<DemandEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull OfferLoader offerLoader;
    @NonNull Converter<List<? extends DemandEntity>, List<Demand>> entitiesToDemandsConverter;

    @Override
    public Demand getById(@NonNull String id) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(TABLE);
        sq.where().eq(ID_POPTAVKY, id);
        DemandEntity entity = DataUtils.requireSingle(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this), DemandEntity.class, id);
        return ListUtil.convertSingle(entity, entitiesToDemandsConverter);
    }


    @Override
    public List<Demand> getAllWithAllOffers(int tenderId) {
        return load(tenderId, true, false, null);
    }


    @Override
    public List<Demand> getAllByTenderWithSupplierOffers(int tenderId, UUID supplierInstitutionAuthorityId) {
        return load(tenderId, false, true, supplierInstitutionAuthorityId);
    }


    @Override
    public List<Demand> getAllByTenderWithoutOffers(int tenderId) {
        return load(tenderId, false, false, null);
    }



    public List<Demand> load(int tenderId, boolean withAllOffers, boolean withSupplierOffers, @Nullable UUID supplierInstitutionAuthorityId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(WHOLE(TABLE));
        sq.from(TABLE);
        sq.joins().add(KAT1_4.TABLE, COLSEQ(TC(TABLE, RECORD_ID), TC(KAT1_4.TABLE, KAT1_4.RECORD_ID)));
        sq.where().eq(FK_RIZENI, tenderId);
        sq.orderBy().addAsc(TC(KAT1_4.TABLE, KAT1_4.NAZEV));

        TimeMeter tm = TimeMeter.start();

        List<DemandEntity> demandEntities = Objects.requireNonNull(jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this));
        log.info("Demands of tender {} load took {}", tenderId, tm.elapsedTimeString());

        List<Demand> demands = Objects.requireNonNull(entitiesToDemandsConverter.convert(demandEntities));

        List<? extends Offer> offersOfTheseDemands;
        List<String> demandIds = ListUtil.getListOfIds(demands);
        boolean toLog = false;

        if (withAllOffers) {
            //admin a knihovnik vidi vsechny nabidky
            offersOfTheseDemands = offerLoader.getAllByDemands(demandIds);
            toLog = true;
        } else if (withSupplierOffers) {
            //dodavatel vidi pouze sve nabidky
            offersOfTheseDemands = offerLoader.getAllByDemandsAndSupplierCompany(demandIds, supplierInstitutionAuthorityId);
            toLog = true;
        } else {
            //ostatni typy uzivatelu nevidi zadne nabidky
            offersOfTheseDemands = List.of();
        }
        if (toLog) {
            log.info("Offers of tender {} load took {}", tenderId, tm.elapsedTimeString());
        }


        demands.forEach(demand -> {
            List<Offer> offersOfThisDemand = ListUtil.filter(offersOfTheseDemands, offer -> offer.getDemandId().equals(demand.getId()));
            demand.setOffers(offersOfThisDemand);
        });

        return demands;
    }


    @Override
    public DemandEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
        String id = rs.getString(ID_POPTAVKY);
        Integer tenderId = DbUtils.getIntegerNotNull(rs, FK_RIZENI);
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD_ID);
        Integer volumeId = DbUtils.getInteger(rs, FK_VOL);
        Integer desiredQuantity = DbUtils.getIntegerNotNull(rs, POC_OBJ);
        @NonNull BigDecimal minEstPriceCzk = DbUtils.bigDecimalNotNull(rs, CENA_KC);
        @NonNull BigDecimal maxEstPriceCzk = DbUtils.bigDecimalNotNull(rs, CENA_KC_MAX);
        Integer daysToSupply = rs.getInt(URG_LHUTA);
        Integer electronicForm = DbUtils.getIntegerNotNull(rs, TYP_DODANI);

        return new DemandEntity(
                id,
                tenderId,
                recordId,
                volumeId,
                desiredQuantity,
                minEstPriceCzk,
                maxEstPriceCzk,
                daysToSupply,
                electronicForm
        );
    }
}
