package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.action.Action;
import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.security.AccessDeniedException;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static java.util.Collections.singletonList;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OfferCreator {

    @NonNull Saver<Offer, Offer> offerSaver;
    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull Provider<@NonNull Integer> offerIdGenerator;
    @NonNull SecurityManager securityManager;
    @NonNull DemandLoader demandLoader;
    @NonNull OfferLoader offerLoader;
    @NonNull ActionSaver actionSaver;
    @NonNull ByIdLoadable<Record, UUID> nonDetailedRecordLoader;

    public Offer create(@NonNull OfferCreationCommand creationCommand, @NonNull OfferRequest request) {
        return readwriteTransactionTemplate.execute(_ -> {
            Demand demand = demandLoader.getById(request.demandId());
            AccessDeniedException.throwIfNot(demand.isAcceptingOffers());

            Record supplierAuthority = nonDetailedRecordLoader.getById(Objects.requireNonNull(creationCommand.activeUser().getRecordId(), "Active user must have authority"));

            List<? extends Offer> existingOffers = offerLoader.getAllByDemandsAndSupplierCompany(singletonList(request.demandId()), supplierAuthority.getId());
            AccessDeniedException.throwIfNot(existingOffers.isEmpty(), "There is offer for given demand already", Texts.ofNative("There is offer for given demand already"));

            Offer offer = new Offer(
                    offerIdGenerator.get(),
                    creationCommand.demandId(),
                    creationCommand.currentAuth().getAuthenticatedUser(),
                    supplierAuthority,
                    creationCommand.quantity(),
                    creationCommand.price(),
                    creationCommand.createDate(),
                    null,
                    creationCommand.note(),
                    null
            );
            securityManager.throwIfCannot(PopsSecurityActions.POPS_OFFER_SAVE, creationCommand.currentAuth(), creationCommand.currentDepartment(), offer);

            offerSaver.save(offer);

            actionSaver.logAction(new Action(creationCommand.session().getId(), Action.TYP_TENDERS, Action.SUBTYPE_TENDERS_OFFER_ADD, String.valueOf(demand.getId())));

            return offer;
        });
    }
}
