package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.io.BranchingFileStreamConsumer;
import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.io.ToByteArraySavingWriteStreamProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.IdentifiedFileImpl;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFileImpl;
import cz.kpsys.portaro.pops.Tender;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

/**
 * Proxy, ktera zavola target saver a nasledne ulozi vytvorene potvrzeni pomoci saveru
 * Created by <PERSON> on 01.09.2015.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FileAgreementCreatorSavingProxy implements FileAgreementCreator {

    @NonNull FileAgreementCreator target;
    @NonNull Saver<Agreement, ?> agreementSaver;
    @NonNull Provider<Integer> portaroUserIdProvider;
    @NonNull Provider<Boolean> enabledProvider;

    @Transactional
    @Override
    public void createBlankAgreementFile(@NonNull Tender tender, @NonNull UUID supplierCompanyAuthorityId, @NonNull FileStreamConsumer streamConsumer) {
        if (!enabledProvider.get()) {
            target.createBlankAgreementFile(tender, supplierCompanyAuthorityId, streamConsumer);
            return;
        }

        ToByteArraySavingWriteStreamProvider toByteArrayWriteHandler = new ToByteArraySavingWriteStreamProvider();
        FileStreamConsumer branchingStreamConsumer = new BranchingFileStreamConsumer(streamConsumer, toByteArrayWriteHandler);

        target.createBlankAgreementFile(tender, supplierCompanyAuthorityId, branchingStreamConsumer);

        String filename = toByteArrayWriteHandler.getFilename();
        Long size = toByteArrayWriteHandler.getSize();
        byte[] data = toByteArrayWriteHandler.getData();
        IdentifiedFile originalFile = IdentifiedFileImpl.createNewFile(filename, size, portaroUserIdProvider.get());
        LoadedIdentifiedFile loadedOriginalFile = new LoadedIdentifiedFileImpl(originalFile, data);
        LoadedSignedFileAgreement agreement = LoadedSignedFileAgreement.createNewBlank(tender.getId(), supplierCompanyAuthorityId, Instant.now(), loadedOriginalFile);

        agreementSaver.save(agreement);
    }
}
