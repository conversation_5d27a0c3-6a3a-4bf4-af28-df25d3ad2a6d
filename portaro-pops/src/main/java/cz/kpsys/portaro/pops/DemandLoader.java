package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;

import java.util.List;
import java.util.UUID;

public interface DemandLoader extends ByIdLoadable<Demand, String> {

    List<Demand> getAllWithAllOffers(int tenderId);

    List<Demand> getAllByTenderWithSupplierOffers(int tenderId, UUID supplierInstitutionAuthorityId);

    List<Demand> getAllByTenderWithoutOffers(int tenderId);
}
