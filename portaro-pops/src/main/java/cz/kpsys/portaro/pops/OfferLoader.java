package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

public interface OfferLoader extends ByIdLoadable<Offer, Integer> {

    List<? extends Offer> getAllByTenderAndSupplierCompany(Tender tender, @Nullable UUID supplierAuthorityId, boolean includeDeleted);

    List<? extends Offer> getAllByDemands(List<String> demandIds);

    List<? extends Offer> getAllByDemandsAndSupplierCompany(List<String> demandIds, @Nullable UUID supplierAuthorityId);
}
