package cz.kpsys.portaro.pops;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OfferFromEntityConverter implements Converter<OfferEntity, Offer> {

    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull ByIdLoadable<Record, UUID> nonDetailedRecordLoader;

    @Override
    public Offer convert(@NonNull OfferEntity source) {
        return new Offer(
                source.getId(),
                source.getDemandId(),
                basicUserLoader.getById(source.getSupplierRepresentativeId()),
                nonDetailedRecordLoader.getById(source.getSupplierRecordId()),
                source.getQuantity(),
                new Price(source.getPriceAmount(), Currency.createDefault()),
                source.getCreateDate(),
                source.getDeleteDate(),
                source.getNote(),
                ObjectUtil.elvis(source.getDeletedByUserId(), basicUserLoader::getById)
        );
    }
}
