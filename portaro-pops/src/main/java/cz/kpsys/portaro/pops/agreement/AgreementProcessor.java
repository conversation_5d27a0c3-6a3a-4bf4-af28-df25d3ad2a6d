package cz.kpsys.portaro.pops.agreement;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;

@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AgreementProcessor {

    @NonNull AgreementCreator agreementCreator;
    @NonNull AgreementConfirmer agreementConfirmer;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull AgreementLoader agreementLoader;

    public Agreement processAgreementConfirmation(@NonNull AgreementConfirmationCommand command) {
        return transactionTemplate.execute(_ -> {
            Agreement loadedAgreement = agreementLoader.getBySupplierAndTender(command.supplier().getRecordId(), command.tenderId())
                    .orElseGet(() -> agreementCreator.create(AgreementCreationCommand.ofButtonNewBlank(command.tenderId(), command.supplier().getRecordId(), Instant.now())));
            agreementConfirmer.confirmAgreement(loadedAgreement);
            return loadedAgreement;
        });
    }
}