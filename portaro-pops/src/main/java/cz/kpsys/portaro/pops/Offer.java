package cz.kpsys.portaro.pops;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.SoftDeletable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
public class Offer extends LabeledId<Integer> implements Identified<Integer>, LabeledIdentified<Integer>, SoftDeletable {

    String demandId;

    @NonFinal
    @Setter
    BasicUser supplierRepresentative;

    @NonFinal
    @Setter
    LabeledIdentified<UUID> supplierAuthority;

    int quantity;

    @NonNull
    Price price;

    @NonNull
    Instant createDate;

    @NonFinal
    @Setter
    Instant deleteDate;

    String note;

    @Nullable
    @Setter
    @NonFinal
    @JsonIgnore
    BasicUser deletedBy;


    public Offer(Integer id,
                 @NonNull String demandId,
                 BasicUser supplierRepresentative,
                 @NonNull LabeledIdentified<UUID> supplierAuthority,
                 int quantity,
                 @NonNull Price price,
                 @NonNull Instant createDate,
                 Instant deleteDate,
                 String note,
                 @Nullable BasicUser deletedBy) {
        super(id, Texts.ofNative("#" + id));
        this.demandId = demandId;
        this.supplierRepresentative = supplierRepresentative;
        this.supplierAuthority = supplierAuthority;
        this.quantity = quantity;
        this.price = price;
        this.createDate = createDate;
        this.deleteDate = deleteDate;
        this.note = note;
        this.deletedBy = deletedBy;
    }

    public Price getTotalPrice() {
        BigDecimal totalPriceValue = getPrice().amount().multiply(BigDecimal.valueOf(getQuantity()));
        return new Price(totalPriceValue, getPrice().currency());
    }

    public Integer getTenderId() {
        if (StringUtil.isNullOrBlank(demandId)) {
            throw new IllegalStateException(String.format("Demand id of offer %s is not set", getId()));
        }
        try {
            String tenderIdString = demandId.substring(0, demandId.indexOf('_'));
            return Integer.parseInt(tenderIdString);
        } catch (NumberFormatException e) {
            throw new IllegalStateException(String.format("Demand id %s of offer %s is not valid", demandId, getId()), e);
        }
    }

    @NonNull
    public Instant getCreateDate() {
        return createDate;
    }

    public Optional<Instant> getDeleteDate() {
        return Optional.ofNullable(deleteDate);
    }

    public void setDeleted(Instant when) {
        this.deleteDate = when;
    }
}
