package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.file.TestTempFileCreator;
import cz.kpsys.portaro.commons.io.ToFileSavingFileStreamConsumer;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.pops.Demand;
import cz.kpsys.portaro.pops.DemandLoader;
import cz.kpsys.portaro.pops.Offer;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordFactory;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.Person;
import lombok.NonNull;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Tag("ci")
@Tag("unit")
public class TenderAgreementCreatorITextPdfTest {

    @Test
    public void testCreateAgreement() {
        Tender tender = Tender.testingStarted(5, Person.testingLibrarian(68768));
        Institution supplierInstitution = Institution.testingSupplierInstitution(5645, UUID.fromString("0d878f84-d58c-4fdc-a331-8c05db1d3cf6"));
        UUID supplierInstitutionAuthority = supplierInstitution.getRecordId();
        Person supplierRepresentative = Person.testing(65521);
        supplierRepresentative.setFirstName("Karel");
        supplierRepresentative.setLastName("Novák");

        //Babička
        Demand babickaDemand = new Demand("5_11", tender, RecordFactory.testingMonography(UUID.fromString("b51522bb-37a6-42b0-8b6c-2464e2747f71"), 110, "Babička"), null, 1, new Price(BigDecimal.valueOf(50), Currency.createDefault()), new Price(BigDecimal.valueOf(50), Currency.createDefault()), 30, false, new ArrayList<>());
        babickaDemand.setOffers(List.of(new Offer(
                1110,
                babickaDemand.getId(),
                supplierRepresentative,
                LabeledId.createWithIdAsNativeText(supplierInstitutionAuthority),
                babickaDemand.getDesiredQuantity(),
                new Price(BigDecimal.valueOf(65.9), Currency.createDefault()),
                Instant.now().minus(5, ChronoUnit.DAYS),
                null,
                "Poznamka",
                null
        )));

        //RUR
        Demand rurDemand = new Demand("5_21", tender, RecordFactory.testingMonography(UUID.fromString("f68fbbdb-bf5c-4a8d-aff9-cfac447e1e82"), 210, "RUR"), null, 5, new Price(BigDecimal.valueOf(50), Currency.createDefault()), new Price(BigDecimal.valueOf(50), Currency.createDefault()), 0, false, new ArrayList<>());
        rurDemand.setOffers(List.of(new Offer(
                1111,
                rurDemand.getId(),
                supplierRepresentative,
                LabeledId.createWithIdAsNativeText(supplierInstitutionAuthority),
                rurDemand.getDesiredQuantity(),
                new Price(BigDecimal.valueOf(81), Currency.createDefault()),
                Instant.now().minus(1, ChronoUnit.DAYS),
                null,
                null,
                null
        )));

        //CHIP - perio
        Demand chip2017Demand = new Demand("5_23", tender, RecordFactory.testingMonography(UUID.fromString("81254607-00bf-4921-b26f-e7bae8d1134e"), 410, "CHIP"), Volume.testing(1001, 2008), 1, new Price(BigDecimal.valueOf(200), Currency.createDefault()), new Price(BigDecimal.valueOf(500), Currency.createDefault()), 0, true, new ArrayList<>());
        chip2017Demand.setOffers(List.of(new Offer(
                1113,
                chip2017Demand.getId(),
                supplierRepresentative,
                LabeledId.createWithIdAsNativeText(supplierInstitutionAuthority),
                chip2017Demand.getDesiredQuantity(),
                new Price(BigDecimal.valueOf(300), Currency.createDefault()),
                Instant.now().minus(1, ChronoUnit.DAYS),
                null,
                null,
                null
        )));

        //Největší obchodník na světě
        Demand nonsDemand = new Demand("5_22", tender, RecordFactory.testingMonography(UUID.fromString("155cd849-a38a-48db-ad4b-627ceb82bdcc"), 310, "Největší obchodník na světě"), null, 2, new Price(BigDecimal.valueOf(50), Currency.createDefault()), new Price(BigDecimal.valueOf(50), Currency.createDefault()), 0, false, new ArrayList<>());
        nonsDemand.setOffers(List.of(new Offer(
                1112,
                nonsDemand.getId(),
                supplierRepresentative,
                LabeledId.createWithIdAsNativeText(supplierInstitutionAuthority),
                nonsDemand.getDesiredQuantity(),
                new Price(BigDecimal.valueOf(0.5), Currency.createDefault()),
                Instant.now().minus(1, ChronoUnit.DAYS),
                null,
                null,
                null
        )));

        final List<Demand> demands = List.of(babickaDemand, rurDemand, chip2017Demand, nonsDemand);


        DemandLoader demandLoader = new DemandLoader() {

            @Override
            public List<Demand> getAllWithAllOffers(int tenderId) {
                return demands;
            }

            @Override
            public List<Demand> getAllByTenderWithSupplierOffers(int tenderId, UUID supplierInstitutionAuthorityId) {
                return demands;
            }

            @Override
            public List<Demand> getAllByTenderWithoutOffers(int tenderId) {
                return demands;
            }

            @Override
            public Demand getById(@NonNull String s) {
                return null;
            }
        };

        IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader = new IdAndIdsLoadable<>() {
            @Override
            public List<Record> getAllByIds(@NonNull List<UUID> uuids) {
                return List.of();
            }

            @Override
            public Record getById(@NonNull UUID uuid) throws ItemNotFoundException {
                RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(uuid, Fond.testingPerson());
                return Record.createAuthority(recordIdFondPair, null, null, false, "Name", null, ObjectProperties.empty());
            }
        };

        FileAgreementCreatorITextPdf creator = new FileAgreementCreatorITextPdf(demandLoader, StaticProvider.of(true), nonDetailedRichRecordLoader);
        creator.createBlankAgreementFile(tender, supplierInstitution.getRecordId(), new ToFileSavingFileStreamConsumer(new TestTempFileCreator(getClass())));
    }
}