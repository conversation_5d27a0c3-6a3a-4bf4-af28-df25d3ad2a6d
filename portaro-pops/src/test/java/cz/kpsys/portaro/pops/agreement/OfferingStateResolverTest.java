package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.pops.Offer;
import cz.kpsys.portaro.pops.OfferLoader;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.BasicUserImpl;
import cz.kpsys.portaro.user.Institution;
import cz.kpsys.portaro.user.Person;
import lombok.NonNull;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.pops.agreement.AgreementStyle.BUTTON;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


@Tag("ci")
@Tag("unit")
public class OfferingStateResolverTest {

    private static final BasicUser TENDER_CREATOR = Person.testingLibrarian(686);
    private static final Tender NOT_STARTED_TENDER = Tender.testingOpenedButNotStarted(123, TENDER_CREATOR);
    private static final Tender STARTED_TENDER = Tender.testingStarted(123, TENDER_CREATOR);
    private static final Tender FINISHED_TENDER = Tender.testingFinished(123, TENDER_CREATOR);
    private static final Institution SUPPLIER_INSTITUTION = Institution.testingSupplierInstitution(456, UUID.fromString("dbadb6f6-0c8b-4624-8ab3-68362c8a8f2f"));
    private static final AgreementLoader agreementLoader = new AgreementLoader() {
        @Override
        public Optional<Agreement> getBySupplierAndTender(UUID supplierAuthorityId, int tenderId) {
            return Optional.of(new ButtonAgreement(
                    UuidGenerator.forIdentifierWithoutDashes(),
                    tenderId,
                    supplierAuthorityId,
                    now().minus(10, DAYS),
                    now().minus(10, DAYS),
                    null
            ));
        }

        @Override
        public Agreement getById(@NonNull String id) throws ItemNotFoundException {
            return new ButtonAgreement(
                    id,
                    1,
                    SUPPLIER_INSTITUTION.getRecordId(),
                    now().minus(10, DAYS),
                    now().minus(10, DAYS),
                    null
            );
        }
    };



    @Test
    public void confirmationStepShouldBeDoneWhenAllOffersAreOlderThanAgreement() {
        Offer o1 = testing(456, now().minus(15, DAYS), null);
        Offer o2 = testing(789, now().minus(11, DAYS), null);
        OfferLoader offerLoader = createOfferLoader(List.of(o1, o2));

        OfferingStateResolver verifier = new OfferingStateResolver(agreementLoader, offerLoader, StaticProvider.of(BUTTON));

        OfferingState result = verifier.getState(STARTED_TENDER, SUPPLIER_INSTITUTION);
        assertTrue(result.getStepOfType(OfferingStepType.AGREEMENT_CONFIRM).done());
        assertFalse(result.getStepOfType(OfferingStepType.TENDER_EVALUATION).done());
    }



    @Test
    public void tenderShouldNotBeAgreedWhenOneOfferIsNewerThanAgreement() throws Exception {
        Offer o1 = testing(456, now().minus(15, DAYS), null);
        Offer o2 = testing(789, now().minus(5, DAYS), null);
        OfferLoader offerLoader = createOfferLoader(List.of(o1, o2));

        OfferingStateResolver verifier = new OfferingStateResolver(agreementLoader, offerLoader, StaticProvider.of(BUTTON));

        OfferingState result = verifier.getState(STARTED_TENDER, SUPPLIER_INSTITUTION);
        assertFalse(result.steps().isEmpty());
        assertFalse(result.steps().stream().allMatch(OfferingStep::done));
    }



    @Test
    public void tenderShouldNotBeAgreedWhenOneOfferIsDeletedAfterAgreement() throws Exception {
        Offer o1 = testing(456, now().minus(15, DAYS), null);
        Offer o2 = testing(789, now().minus(20, DAYS), now().minus(5, DAYS));
        OfferLoader offerLoader = createOfferLoader(List.of(o1, o2));

        OfferingStateResolver verifier = new OfferingStateResolver(agreementLoader, offerLoader, StaticProvider.of(BUTTON));

        OfferingState result = verifier.getState(STARTED_TENDER, SUPPLIER_INSTITUTION);
        assertFalse(result.steps().isEmpty());
        assertFalse(result.steps().stream().allMatch(OfferingStep::done));
    }



    @Test
    public void confirmationStepShouldBeNonFeasibleWhenTenderFinishedAndSupplierDontHaveAnyOffers() throws Exception {
        OfferLoader offerLoader = createOfferLoader(List.of());

        OfferingStateResolver verifier = new OfferingStateResolver(agreementLoader, offerLoader, StaticProvider.of(BUTTON));

        OfferingState result = verifier.getState(FINISHED_TENDER, SUPPLIER_INSTITUTION);
        assertFalse(result.steps().isEmpty());
        assertFalse(result.getStepOfType(OfferingStepType.AGREEMENT_CONFIRM).feasible());
    }



    @Test
    public void shouldReturnAllStepsWhenTenderFinishedAndSupplierDontHaveAnyOffers() throws Exception {
        Offer o1 = testing(456, now().minus(15, DAYS), null);
        OfferLoader offerLoader = createOfferLoader(List.of(o1));

        OfferingStateResolver verifier = new OfferingStateResolver(agreementLoader, offerLoader, StaticProvider.of(BUTTON));

        OfferingState result = verifier.getState(FINISHED_TENDER, SUPPLIER_INSTITUTION);
        assertFalse(result.steps().isEmpty());
        assertTrue(result.steps().stream().allMatch(OfferingStep::done));
    }


    private static OfferLoader createOfferLoader(List<Offer> returningOffers) {
        return new OfferLoader() {
                @Override
                public List<? extends Offer> getAllByTenderAndSupplierCompany(Tender tender, @Nullable UUID supplierAuthorityId, boolean includeDeleted) {
                    return returningOffers;
                }

                @Override
                public List<? extends Offer> getAllByDemands(List<String> demandIds) {
                    return List.of();
                }

                @Override
                public List<? extends Offer> getAllByDemandsAndSupplierCompany(List<String> demandIds, @Nullable UUID supplierAuthorityId) {
                    return List.of();
                }

                @Override
                public Offer getById(@NonNull Integer id) {
                    return null;
                }
            };
    }

    public static Offer testing(Integer id,
                                Instant createDate,
                                Instant deleteDate) {
        return new Offer(
                id,
                "4_21_",
                BasicUserImpl.testing(4684),
                LabeledId.createWithIdAsNativeText(UUID.fromString("78e1fe03-0954-4e65-84d5-b4fffa31a876")),
                1,
                new Price(BigDecimal.valueOf(681), Currency.createDefault()),
                createDate,
                deleteDate,
                null,
                null);
    }
}