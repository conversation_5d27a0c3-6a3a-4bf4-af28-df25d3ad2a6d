package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.io.FileStreamConsumer;
import cz.kpsys.portaro.commons.io.ToByteArraySavingFileStreamConsumer;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.pops.Tender;
import cz.kpsys.portaro.user.Person;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.lang.Nullable;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class TenderAgreementCreatorSavingProxyTest {

    @Test
    public void proxiedResultShouldBeSameAsOriginal() {
        FileAgreementCreator target = (tender, _, handler) -> {
            byte[] bytes = "Obsah mockoveho agreementu".getBytes();
            FileStreamConsumer.streamBytes(handler, "MockAgreement.txt", bytes);
        };

        ToVariableAgreementSaver agreementSaver = new ToVariableAgreementSaver();
        FileAgreementCreatorSavingProxy proxy = new FileAgreementCreatorSavingProxy(target, agreementSaver, StaticProvider.of(123), StaticProvider.of(true));

        ToByteArraySavingFileStreamConsumer mockOriginalHandler = new ToByteArraySavingFileStreamConsumer();
        proxy.createBlankAgreementFile(
                Tender.testingStarted(123, Person.testingLibrarian(68768)),
                UUID.fromString("72699b7e-e64e-4eb7-8120-ca2f0db937b3"),
                mockOriginalHandler);

        assertEquals(mockOriginalHandler.getFilename(), ((SignedFileAgreement) agreementSaver.savedObject).getOriginalFile().getFilename());
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class ToVariableAgreementSaver implements Saver<Agreement, Agreement> {

        @Nullable
        @NonFinal
        Agreement savedObject;

        @Override
        public @NonNull Agreement save(@NonNull Agreement agreement) {
            savedObject = agreement;
            return savedObject;
        }

    }
}