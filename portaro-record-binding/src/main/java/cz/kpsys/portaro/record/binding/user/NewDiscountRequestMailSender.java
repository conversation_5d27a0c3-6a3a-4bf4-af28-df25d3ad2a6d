package cz.kpsys.portaro.record.binding.user;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.mail.*;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.UserStringGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class NewDiscountRequestMailSender {

    @NonNull TemplateEngine templateEngine;
    @NonNull MailService mailService;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull ContextualProvider<Department, @NonNull Locale> defaultLocaleProvider;
    @NonNull ContextualProvider<Department, @Nullable String> newDiscountRequestNotificationEmail;

    public void send(SendNewDiscountNotificationCommand command) {
        String notificationAddress = newDiscountRequestNotificationEmail.getOn(command.ctx());
        if (notificationAddress != null) {
            Map<String, Object> model = new HashMap<>();
            model.put("user", command.user());
            String body = templateEngine.build(UserConstants.Template.TEMPLATE_DISCOUNT_REQUEST, command.currentAuth(), command.ctx(), model);

            mailService.sendRawBodyMail(createMail(notificationAddress, command, body));
        } else {
            log.info("Email could not be sent because notification email address was not set.");
        }
    }


    private NonspecificMailSendCommand createMail(String toEmail, SendNewDiscountNotificationCommand command, String body) {
        return new NonspecificMailSendCommand(
                RawBodyMailTopics.COMMON,
                command.ctx(),
                From.system(),
                To.emailWithForcedLocale(toEmail, defaultLocaleProvider.getOn(command.ctx())),
                Texts.ofNative(String.format("Uživatel %s požádal o slevu.", prettyUserNameGenerator.generate(command.user()))),
                body,
                List.of(),
                command.currentAuth()
        );
    }

}