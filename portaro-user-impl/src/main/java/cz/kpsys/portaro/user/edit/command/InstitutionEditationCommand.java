package cz.kpsys.portaro.user.edit.command;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.record.Record;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class InstitutionEditationCommand extends UserEditationCommand {

    @Nullable Optional<@NullableNotBlank String> institutionName;

    @Nullable Optional<@NullableNotBlank String> institutionIco;

    @Nullable Optional<@NullableNotBlank String> institutionDic;

    @Nullable Optional<@NullableNotBlank String> homepageUrl;
    
    public static InstitutionEditationCommand withSupplierAccount(Record supplierCompanyAuthority) {
        InstitutionEditationCommand command = new InstitutionEditationCommand();
        command.setRecord(supplierCompanyAuthority);
        command.setSupplierAccounts(Optional.of(List.of(new SupplierAccountEditationCommand())));
        return command;
    }
}
