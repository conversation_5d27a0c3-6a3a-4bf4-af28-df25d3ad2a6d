package cz.kpsys.portaro.user.edit.command;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.PhoneNumberEditationCommand;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.contact.UserEmailEditationCommand;
import cz.kpsys.portaro.user.prop.UserServicePropertyCommand;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@FieldNameConstants
@ToString
public class UserEditationCommand implements Identified<Integer> {

    public boolean isCreation() {
        return id == null;
    }

    public boolean isEvided() {
        return BasicUser.isEvided(id);
    }

    public static final String OBJECT_NAME = "user.edit";

    @EqualsAndHashCode.Include
    @Nullable
    Integer id;

    @Setter(AccessLevel.NONE)
    @Nullable
    Optional<UUID> rid;

    @Setter(AccessLevel.NONE)
    @Nullable
    Optional<UUID> recordId;

    @Nullable
    Optional<Boolean> active;

    @Nullable
    Optional<List<ReaderAccountEditationCommand>> readerAccounts;

    @Nullable
    Optional<List<EditorAccountEditationCommand>> editorAccounts;

    @Nullable
    Optional<List<SupplierAccountEditationCommand>> supplierAccounts;

    @Nullable
    Optional<SourcedEditableList<UserEmailEditationCommand>> emails;

    @Nullable
    Optional<SourcedEditableList<PhoneNumberEditationCommand>> phoneNumbers;

    @Nullable
    Optional<SourcedEditableList<UserAddressEditationCommand>> addresses;

    @Nullable
    Optional<@NullableNotBlank @Size(min = 1, max = User.USERNAME_MAX_LENGTH) String> username;

    @Nullable
    Optional<@NullableNotBlank @Size(min = 1, max = User.SYNC_ID_LENGTH) String> syncId;

    @Nullable
    Optional<@NullableNotBlank @Size(min = User.PASSWORD_MIN_LENGTH, max = User.PASSWORD_MAX_LENGTH) String> newPassword;

    @Nullable
    Optional<SimpleEditableList<Department, DepartmentEditationMode>> readableDepartments;

    @Nullable
    Optional<SimpleEditableList<Department, DepartmentEditationMode>> editableDepartments;

    @Nullable
    Optional<List<Fond>> editableFonds;

    @Nullable
    Optional<SimpleEditableList<UserServicePropertyCommand, UserServicePropertiesEditationMode>> userServiceProperties;

    public void setRecord(Record record) {
        this.rid = Optional.ofNullable(record.getId());
        this.recordId = Optional.ofNullable(record.getId());
    }

}
