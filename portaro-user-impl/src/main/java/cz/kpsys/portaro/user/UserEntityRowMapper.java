package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.entity.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.lang.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

import static cz.kpsys.portaro.commons.util.StringUtil.notBlankTrimmedString;
import static cz.kpsys.portaro.commons.util.StringUtil.requireNotBlank;
import static cz.kpsys.portaro.user.UserLoaderConstants.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserEntityRowMapper implements RowMapper<UserEntity> {

    @NonNull Codebook<SourceOfData, String> contactSourceLoader;
    @NonNull Codebook<Gender, String> genderLoader;

    @NonNull
    @Override
    public UserEntity mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        return new UserEntity(
                DbUtils.getIntegerNotNull(rs, UZIVATELE__ID_UZIV),
                DbUtils.uuidOrNull(rs, UZIVATELE__ID),
                notBlankTrimmedString(rs.getString(UZIVATELE__ZOBR_JMENO_JP)),
                DbUtils.uuidNotNull(rs, UZIVATELE__CREATION_EVENT_ID),
                DbUtils.uuidOrNull(rs, UZIVATELE__ACTIVATION_EVENT_ID),
                DbUtils.uuidOrNull(rs, UZIVATELE__ANONYMIZATION_EVENT_ID),
                DbUtils.uuidOrNull(rs, UZIVATELE__DELETION_EVENT_ID),
                notBlankTrimmedString(rs.getString(UZIVATELE__USERNAME)),
                notBlankTrimmedString(rs.getString(UZIVATELE__PASSWORD)),
                notBlankTrimmedString(rs.getString(UZIVATELE__SYNC_ID)),
                DbUtils.uuidOrNull(rs, UZIVATELE__RECORD_ID),
                mapPersonEntity(rs),
                mapInstitutionEntity(rs),
                mapLibraryEntity(rs),
                mapSoftwareEntity(rs),
                mapReaderAccountEntity(rs),
                mapEditorAccountEntity(rs),
                mapSupplierAccountEntity(rs)
        );
    }

    @Nullable
    private PersonEntity mapPersonEntity(ResultSet rs) throws SQLException {
        Integer personUserId = DbUtils.getInteger(rs, OSOBY__FK_UZIV);
        if (personUserId == null) {
            return null;
        }
        return new PersonEntity(
                personUserId,
                notBlankTrimmedString(rs.getString(OSOBY__JMENO)),
                notBlankTrimmedString(rs.getString(OSOBY__MIDDLE_NAME)),
                notBlankTrimmedString(rs.getString(OSOBY__PRIJMENI)),
                DbUtils.getOptionalNotBlankTrimmedString(rs, OSOBY__NAME_SOURCE).map(contactSourceLoader::getById).orElse(null),
                notBlankTrimmedString(rs.getString(OSOBY__TITUL)),
                notBlankTrimmedString(rs.getString(OSOBY__TITUL_ZA)),
                DbUtils.getOptionalNotBlankTrimmedString(rs, OSOBY__DEGREE_SOURCE).map(contactSourceLoader::getById).orElse(null),
                DbUtils.getOptionalNotBlankTrimmedString(rs, OSOBY__GENDER).map(genderLoader::getById).orElse(null),
                DbUtils.getOptionalNotBlankTrimmedString(rs, OSOBY__GENDER_SOURCE).map(contactSourceLoader::getById).orElse(null),
                DbUtils.getLocalDateOrNull(rs, OSOBY__DAT_NAR),
                DbUtils.getLocalDateOrNull(rs, OSOBY__DEATH_DATE),
                DbUtils.getOptionalNotBlankTrimmedString(rs, OSOBY__LIFE_DATE_SOURCE).map(contactSourceLoader::getById).orElse(null),
                notBlankTrimmedString(rs.getString(OSOBY__ZAMESTNANI)),
                notBlankTrimmedString(rs.getString(OSOBY__ADR_ZAM)),
                notBlankTrimmedString(rs.getString(OSOBY__VZDELANI)),
                notBlankTrimmedString(rs.getString(OSOBY__TRIDA)),
                notBlankTrimmedString(rs.getString(OSOBY__CISOP)),
                notBlankTrimmedString(rs.getString(OSOBY__OSOBA_GUID)),
                notBlankTrimmedString(rs.getString(OSOBY__NET_ID)),
                notBlankTrimmedString(rs.getString(OSOBY__OPENID)),
                notBlankTrimmedString(rs.getString(OSOBY__BAKALARI)),
                notBlankTrimmedString(rs.getString(OSOBY__SOL_ID)),
                rs.getBoolean(OSOBY__MOJEID_JE_VALID),
                rs.getTimestamp(OSOBY__MOJEID_POSL_UPDATE_TIME)
        );
    }

    @Nullable
    private static InstitutionEntity mapInstitutionEntity(ResultSet rs) throws SQLException {
        Integer institutionUserId = DbUtils.getInteger(rs, INSTITUCE__FK_UZIV);
        if (institutionUserId == null) {
            return null;
        }
        return new InstitutionEntity(
                institutionUserId,
                requireNotBlank(rs.getString(INSTITUCE__NAZEV)),
                DbUtils.getIntegerNotNull(rs, INSTITUCE__TYP_INSTITUCE),
                notBlankTrimmedString(rs.getString(INSTITUCE__ICO)),
                notBlankTrimmedString(rs.getString(INSTITUCE__DIC)),
                notBlankTrimmedString(rs.getString(INSTITUCE__HOMEPAGE_URL))
        );
    }

    @Nullable
    private static LibraryEntity mapLibraryEntity(ResultSet rs) throws SQLException {
        Integer libraryUserId = DbUtils.getInteger(rs, KNIHOVNY__FK_UZIV);
        if (libraryUserId == null) {
            return null;
        }
        return new LibraryEntity(
                libraryUserId,
                notBlankTrimmedString(rs.getString(KNIHOVNY__SIGLA)),
                DbUtils.getInteger(rs, KNIHOVNY__FK_PUJC)
        );
    }

    @Nullable
    private static SoftwareEntity mapSoftwareEntity(ResultSet rs) throws SQLException {
        Integer softwareUserId = DbUtils.getInteger(rs, SOFTWARE__FK_UZIV);
        if (softwareUserId == null) {
            return null;
        }
        return new SoftwareEntity(
                softwareUserId,
                requireNotBlank(rs.getString(SOFTWARE__NAZEV)),
                rs.getBoolean(SOFTWARE__WEB_CRAWLER)
        );
    }

    @Nullable
    private static ReaderAccountEntity mapReaderAccountEntity(ResultSet rs) throws SQLException {
        Integer readerAccountUserId = DbUtils.getInteger(rs, CTENARI__FK_UZIV);
        if (readerAccountUserId == null) {
            return null;
        }
        return new ReaderAccountEntity(
                readerAccountUserId,
                notBlankTrimmedString(rs.getString(CTENARI__CIS_LEG)),
                notBlankTrimmedString(rs.getString(CTENARI__BAR_COD)),
                DbUtils.getLocalDateOrNull(rs, CTENARI__DAT_REG),
                DbUtils.getLocalDateOrNull(rs, CTENARI__DATP_REG),
                DbUtils.getLocalDateOrNull(rs, CTENARI__KON_REG),
                Objects.requireNonNull(rs.getString(CTENARI__FK_CTENKAT)),
                DbUtils.getIntegerNotNull(rs, CTENARI__FK_PUJC),
                !rs.getBoolean(CTENARI__JE_POVOL),
                rs.getBoolean(CTENARI__JE_BLOKOVAN),
                notBlankTrimmedString(rs.getString(CTENARI__POZNAMKA)),
                notBlankTrimmedString(rs.getString(CTENARI__VZKAZ)),
                notBlankTrimmedString(rs.getString(CTENARI__RFID_UID)),
                DbUtils.getIntegerNotNull(rs, CTENARI__TYP_TISK_UPOM),
                DbUtils.getIntegerNotNull(rs, CTENARI__TYP_TISK_REZE)
        );
    }

    @Nullable
    private static EditorAccountEntity mapEditorAccountEntity(ResultSet rs) throws SQLException {
        Integer editorAccountUserId = DbUtils.getInteger(rs, KNIHOVNICI__FK_UZIV);
        if (editorAccountUserId == null) {
            return null;
        }
        return new EditorAccountEntity(
                editorAccountUserId,
                rs.getBoolean(KNIHOVNICI__JE_SERVIS_WWW),
                notBlankTrimmedString(rs.getString(KNIHOVNICI__VAL_KOD_WWW)),
                DbUtils.getInteger(rs, KNIHOVNICI__FK_GROUP),
                DbUtils.getIntegerNotNull(rs, KNIHOVNICI__UROVEN),
                rs.getBoolean(KNIHOVNICI__JE_POVOL)
        );
    }

    @Nullable
    private static SupplierAccountEntity mapSupplierAccountEntity(ResultSet rs) throws SQLException {
        Integer supplierAccountUserId = DbUtils.getInteger(rs, DODAVATELE__FK_UZIV);
        if (supplierAccountUserId == null) {
            return null;
        }
        return new SupplierAccountEntity(supplierAccountUserId);
    }
}
