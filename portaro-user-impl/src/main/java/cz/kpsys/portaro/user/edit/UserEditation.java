package cz.kpsys.portaro.user.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.holding.RecordHoldingUpsertCommand;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.edit.command.UserEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserEditationCommandConverter;
import cz.kpsys.portaro.user.edit.command.UserRecordEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserSaveCommand;
import cz.kpsys.portaro.user.edit.handler.AfterSaveHandlerInput;
import cz.kpsys.portaro.user.edit.handler.CompositeAfterSaveHandler;
import cz.kpsys.portaro.user.edit.modifier.CompositeContextualBeforeSaveModifier;
import cz.kpsys.portaro.user.edit.modifier.ContextualBeforeSaveModifier;
import cz.kpsys.portaro.user.edit.validation.UserValidator;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class UserEditation<UEC extends UserEditationCommand, USER extends User> {

    @NonNull Department currentDepartment;
    @NonNull UserAuthentication currentAuth;
    @NonNull Eventer eventer;
    @NonNull Saver<UserSaveCommand<?>, User> userSaver;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull PasswordEncoder passwordEncoder;
    @NonNull UserEditationCommandConverter userEditationCommandConverter;
    @NonNull UserEditationRequestApplier userEditationRequestApplier;
    @NonNull UserValidator userValidator;
    @NonNull ContextualBeforeSaveModifier<Department> compositeContextualBeforeSaveModifier;
    @NonFinal
    @NonNull
    UserModificationResult<USER> userResult;
    @NonNull CompositeAfterSaveHandler<USER> afterSaveHandler;
    @Getter
    @NonFinal
    boolean creation;
    @NonFinal
    AfterSaveHandlerInput afterSaveHandlerInput;
    @NonFinal boolean validateBeforeSave = true;

    @NonFinal @Nullable RecordEditation userRecordEditation;

    @NonFinal @NonNull List<RecordEditation> dependentRecordEditations = List.of();


    public UserEditation(@NonNull USER user,
                         @NonNull Department currentDepartment,
                         @NonNull UserAuthentication currentAuth,
                         @NonNull Eventer eventer,
                         @NonNull Saver<UserSaveCommand<?>, User> userSaver,
                         @NonNull RecordHoldingUpserter recordHoldingUpserter,
                         @NonNull PasswordEncoder passwordEncoder,
                         @NonNull UserEditationCommandConverter userEditationCommandConverter,
                         @NonNull UserEditationRequestApplier userEditationRequestApplier,
                         @NonNull UserValidator userValidator,
                         @NonNull CompositeContextualBeforeSaveModifier<Department> compositeContextualBeforeSaveModifier,
                         @NonNull CompositeAfterSaveHandler<USER> afterSaveHandler) {
        this.currentDepartment = currentDepartment;
        this.creation = !DataUtils.isPersisted(user);
        this.userResult = new UserModificationResult<>(user, this.creation, Optional.empty());
        this.userValidator = userValidator;
        this.currentAuth = currentAuth;
        this.eventer = eventer;
        this.userSaver = userSaver;
        this.recordHoldingUpserter = recordHoldingUpserter;
        this.passwordEncoder = passwordEncoder;
        this.userEditationCommandConverter = userEditationCommandConverter;
        this.userEditationRequestApplier = userEditationRequestApplier;
        this.compositeContextualBeforeSaveModifier = compositeContextualBeforeSaveModifier;
        this.afterSaveHandler = afterSaveHandler;
        LocalDate currentRegistrationExpiration = user.getReaderAccounts().stream()
                .findFirst()
                .flatMap(ReaderRole::getRegistrationExpirationDate)
                .orElse(null);
        this.afterSaveHandlerInput = new AfterSaveHandlerInput(currentRegistrationExpiration);
    }

    public @NonNull USER user() {
        return userResult.user();
    }

    public UserEditation<UEC, USER> skipValidation() {
        validateBeforeSave = false;
        return this;
    }

    /**
     * Deprecated - use apply(Consumer&lt;UER&gt; emptyRequestModifier)
     */
    @Deprecated
    public UserEditation<UEC, USER> modify(Consumer<UEC> filledRequestModifier, SourceOfData sourceOfData) {
        UEC filledRequest = toCommand(sourceOfData);
        filledRequestModifier.accept(filledRequest);
        return apply(filledRequest);
    }

    public UserEditation<UEC, USER> apply(Consumer<UEC> emptyRequestModifier) {
        UEC emptyRequest = userEditationCommandConverter.createObject(user());
        emptyRequestModifier.accept(emptyRequest);
        return apply(emptyRequest);
    }

    public UserEditation<UEC, USER> apply(Consumer<UEC> fillRequestModifier, SourceOfData sourceOfData) {
        UEC command = userEditationCommandConverter.toCommand(user(), sourceOfData);
        fillRequestModifier.accept(command);
        return apply(command);
    }

    public UserEditation<UEC, USER> apply(UEC userEditationRequest) {
        setResult(userEditationRequestApplier.applyRequest(user(), currentDepartment, currentAuth, userEditationRequest));
        return this;
    }

    public UserEditation<UEC, USER> map(Function<UserRecordEditationCommand<UEC>, UserRecordEditationCommand<UEC>> emptyRequestModifier) {
        UEC emptyRequest = userEditationCommandConverter.createObject(user());
        return apply(emptyRequestModifier.apply(
                new UserRecordEditationCommand<>(emptyRequest, userRecordEditation, dependentRecordEditations)));
    }

    public UserEditation<UEC, USER> apply(UserRecordEditationCommand<UEC> userEditationRequest) {
        this.userRecordEditation = userEditationRequest.recordEditation();
        this.dependentRecordEditations = userEditationRequest.dependentRecordEditations();
        return apply(userEditationRequest.userEditationCommand());
    }

    public UserEditation<UEC, USER> touch(Consumer<User> edit) {
        edit.accept(user());
        userDataModified(true);
        return this;
    }

    public UserEditation<UEC, USER> setPassword(@NonNull String password) {
        eventer.save(Event.Codes.USER_PASSWORD_SETTING, currentAuth, currentDepartment, null);
        user().setPasswordHash(passwordEncoder.encode(password));
        userDataModified(true);
        return this;
    }

    public UserEditation<UEC, USER> addPhoneNumberWhenNotExistsByTypeAndValue(@NonNull Contact contact) {
        if (user().getPhoneNumbers().stream().noneMatch(contact::typeAndValueEquals)) {
            user().getPhoneNumbers().add(contact);
            userDataModified(true);
        }
        return this;
    }

    public UserEditation<UEC, USER> addEmailWhenNotExistsByValue(@NonNull Contact email) {
        if (user().getEmails().stream().noneMatch(email::typeAndValueEquals)) {
            user().getEmails().add(email);
            userDataModified(true);
        }
        return this;
    }

    public UserEditation<UEC, USER> removeEmailWhenNotExistsByTypeAndValue(@NonNull String contactValue) {
        userDataModified(user().getEmails().removeIf(c -> c.getValue().equals(contactValue)));
        return this;
    }

    public UserEditation<UEC, USER> removePhoneNumberWhenNotExistsByTypeAndValue(@NonNull String contactValue) {
        userDataModified(user().getPhoneNumbers().removeIf(c -> c.getValue().equals(contactValue)));
        return this;
    }

    public boolean isValid() {
        UserSaveCommand<USER> saveRequest = new UserSaveCommand<>(user(), currentAuth, currentDepartment, creation, activation());
        try {
            userValidator.validate(saveRequest);
            return true;
        } catch (ConstraintViolationException ex) {
            return false;
        }
    }

    public UserEditationSaveResult<USER> saveIfModified() {

        UserSaveCommand<USER> saveRequest = new UserSaveCommand<>(user(), currentAuth, currentDepartment, creation, activation());

        if (validateBeforeSave) {
            try {
                userValidator.validate(saveRequest);
            } catch (RuntimeException ex) {
                user().setActivationEventId(activation() ? null : user().getActivationEventId());
                throw ex;
            }
        }

        userDataModified(compositeContextualBeforeSaveModifier.modify(currentDepartment, saveRequest));

        if (userDataModified()) {
            saveRecords(saveRequest.user());
            userSaver.save(saveRequest);
            log.info("Saved user {}", userResult);

            afterSaveHandler.handle(afterSaveHandlerInput, saveRequest);

            UserEditationSaveResult<USER> result = new UserEditationSaveResult<>(true, user(), creation, activation(), selfEditing());
            creation = false;
            return result;
        } else {
            return new UserEditationSaveResult<>(false, user(), false, false, selfEditing());
        }
    }

    private void saveUserRecord(@NonNull @Valid USER user) {
        if (userRecordEditation == null) {
            return;
        }
        if (userRecordEditation.isDraft()) {
            userRecordEditation.publish(currentDepartment, currentAuth);
        } else {
            userRecordEditation.saveIfModified(currentDepartment, currentAuth);
        }

        // TODO: delete old holdings when removing readable departments
        for (Department readableDepartment : user.getReadableDepartments()) {
            recordHoldingUpserter.upsert(new RecordHoldingUpsertCommand(userRecordEditation.getRecord(), readableDepartment, false, currentDepartment, currentAuth));
        }
    }

    private void saveDependentRecords() {
        for (RecordEditation dependentRecordEditation : dependentRecordEditations) {
            if (dependentRecordEditation.isDraft()) {
                dependentRecordEditation.publish(currentDepartment, currentAuth);
            } else {
                dependentRecordEditation.saveIfModified(currentDepartment, currentAuth);
            }
        }
    }

    private void saveRecords(@NonNull @Valid USER user) {
        saveUserRecord(user);
        saveDependentRecords();
    }

    public UEC toCommand(SourceOfData sourceOfData) {
        return userEditationCommandConverter.toCommand(user(), sourceOfData);
    }

    private void userDataModified(boolean modified) {
        userResult = userResult.withModified(userDataModified() | modified);
    }

    private void setResult(UserModificationResult<User> userUserModificationResult) {
        userResult = userResult.withUser((USER) userUserModificationResult.user());
        if (userUserModificationResult.activation().isPresent()) {
            userResult = userResult.withActivation(userUserModificationResult.activation());
        }
        userDataModified(userUserModificationResult.modified());
    }

    private boolean activation() {
        return userResult.activation().orElse(false);
    }

    private boolean selfEditing() {
        return currentAuth.getActiveUser().equals(user());
    }

    private boolean userDataModified() {
        return userResult.modified();
    }
}
