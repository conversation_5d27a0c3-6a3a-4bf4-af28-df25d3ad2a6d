package cz.kpsys.portaro.user;

import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.appserver.dml.TableWriteGenerator;
import cz.kpsys.portaro.appserver.dml.TableWriteMode;
import cz.kpsys.portaro.appserver.mapping.AppserverSaveResponseHandler;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.auth.LastModificationUserAuthResolver;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.geo.Country;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.databasestructure.UserDb.*;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.user.contact.*;
import cz.kpsys.portaro.user.deletion.ReaderDeletionCommand;
import cz.kpsys.portaro.user.edit.command.UserSaveCommand;
import cz.kpsys.portaro.user.role.reader.CardNumberSequenceItem;
import cz.kpsys.portaro.user.role.reader.CardNumberSequenceItemLoader;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cz.kpsys.portaro.databasestructure.UserServicePropertyDb.USER_SERVICE_PROPERTY;
import static java.util.stream.Stream.concat;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserSaverAppserver implements Saver<UserSaveCommand<?>, User> {

    @NonNull DmlAppserverService dmlService;
    @NonNull AddressesCreator addressesCreator;
    @NonNull UserAddressLoader userAddressLoader;
    @NonNull PhoneNumberValidator phoneNumberValidator;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull UserStringGenerator sortableUserNameGenerator;
    @NonNull Provider<@NonNull Boolean> autogenerateUserCardNumberProvider;
    @NonNull CardNumberSequenceItemLoader cardNumberSequenceItemLoader;
    @NonNull TableWriteGenerator<CardNumberSequenceItem> cardNumberSequenceItemTableWriteGenerator;
    @NonNull Deleter<ReaderDeletionCommand> readerDeleter;
    @NonNull ByIdLoadable<User, Integer> userLoader;
    @NonNull LastModificationUserAuthResolver lastModificationUserAuthResolver;
    @NonNull Eventer eventer;


    @Override
    public @NonNull User save(@NonNull UserSaveCommand<?> er) {
        List<TableWrite> tableWrites = generateDmls(er);
        if (er.creation()) {
            Integer idFromResponse = dmlService.executeStatement(tableWrites, AppserverSaveResponseHandler.forConcreteColumnIntegerId(UZIVATELE.TABLE, UZIVATELE.ID_UZIV));
            Objects.requireNonNull(idFromResponse, "Unknown error - created user has not given id"); //kontrola dostani idecka
            er.user().setId(idFromResponse);

        } else {
            dmlService.executeStatement(tableWrites, NoOpAppserverResponseHandler.create());
        }

        return er.user();
    }


    private List<TableWrite> generateDmls(UserSaveCommand<?> req) {
        List<TableWrite> tableWrites = new ArrayList<>();
        User user = req.user();

        if (req.creation()) {
            Event event = eventer.save(Event.Codes.USER_CREATION, req.currentAuth(), req.ctx(), null);
            user.setCreationEventId(event.getId());
        }

        if (req.activation()) {
            Assert.state(user.getActivationEventId() != null, "Save request is marked as activation, but user has no activation event id");
            Event event = eventer.save(user.getActivationEventId(), Event.Codes.USER_ACTIVATION, req.currentAuth(), req.ctx(), null);
            user.setActivationEventId(event.getId());
        }

        tableWrites.addAll(generateWritesToUserTable(user));

        switch (user) {
            case Person person -> tableWrites.addAll(generateWritesToPersonTable(person));
            case Institution institution -> {
                tableWrites.addAll(generateWritesToInstitutionTable(institution));
                if (user instanceof Library library) {
                    tableWrites.addAll(generateWritesToLibraryTable(library));
                }
            }
            case Software software -> tableWrites.addAll(generateWritesToSoftwareTable(software));
            default -> throw new UnsupportedOperationException("User type " + user.getClass().getSimpleName() + " is not supported");
        }

        tableWrites.addAll(syncReaderAccounts(user, req.ctx(), req.currentAuth()));

        tableWrites.addAll(syncEditorAccounts(user));

        tableWrites.addAll(syncSupplierAccounts(user));

        tableWrites.addAll(syncContacts(user));

        tableWrites.addAll(syncUserAddresses(user));

        tableWrites.addAll(syncUserServiceProperties(user));

        tableWrites.addAll(syncReadableDepartments(user));

        tableWrites.addAll(syncEditableDepartments(user));

        tableWrites.addAll(syncEditableFonds(user));

        tableWrites.addAll(syncSequences(user));

        return tableWrites;
    }


    private List<TableWrite> generateWritesToUserTable(User user) {
        TableWrite tableWrite = TableWrite.createUpsert(UZIVATELE.TABLE)
                .addWhereCol(UZIVATELE.ID_UZIV, user.isEvided() ? user.getId() : null)
                .addCol(UZIVATELE.ZOBR_JMENO_JP, prettyUserNameGenerator.generate(user))
                .addCol(UZIVATELE.ZOBR_JMENO, sortableUserNameGenerator.generate(user))
                .addCol(UZIVATELE.TRIDJMENO, StringUtil.limitCharsAndTrimWithoutEllipsis(StringUtil.replaceNonAsciiCharsToCp1250(sortableUserNameGenerator.generate(user).toUpperCase()), BasicUser.PRINTABLE_NAME_SORTER_MAX_LENGTH, true))
                .addCol(UZIVATELE.FLATJMENO, StringUtil.limitCharsAndTrimWithoutEllipsis(StringUtil.flatString(sortableUserNameGenerator.generate(user).toUpperCase()), BasicUser.PRINTABLE_NAME_FLAT_MAX_LENGTH, true))
                .addCol(UZIVATELE.USERNAME, user.getUsername())
                .addCol(UZIVATELE.CREATION_EVENT_ID, user.getCreationEventId())
                .addCol(UZIVATELE.ACTIVATION_EVENT_ID, user.getActivationEventId())
                .addCol(UZIVATELE.ANONYMIZATION_EVENT_ID, user.getAnonymizationEventId())
                .addCol(UZIVATELE.DELETION_EVENT_ID, user.getDeletionEventId())
                // In case of adding new synchronizer, this needs to be adjusted
                .addCol(UZIVATELE.SYNC_ID, user.getSyncId())
                .addCol(UZIVATELE.RECORD_ID, user.getRecordId())
                .addCol(UZIVATELE.ID, user.getRid());
        if (!user.isEvided()) {
            tableWrite.addDateCol(UZIVATELE.DATCAS_ZALOZENI, new Date(), true);
        }
        if (user.getPasswordHash() != null) {
            tableWrite.addCol(UZIVATELE.PWD, user.getPasswordHash());
        }
        return List.of(tableWrite);
    }


    private List<TableWrite> generateWritesToPersonTable(Person person) {
        TableWrite tableWrite = TableWrite.createUpsert(OSOBY.TABLE)
                .addReferringWhereCol(OSOBY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                .addCol(OSOBY.JMENO, person.getFirstName())
                .addCol(OSOBY.MIDDLE_NAME, person.getMiddleName())
                .addCol(OSOBY.PRIJMENI, StringUtil.notNullString(person.getLastName()))
                .addCol(OSOBY.NAME_SOURCE, ObjectUtil.getIdOrNull(person.getNameSource()))
                .addCol(OSOBY.TITUL, person.getDegree())
                .addCol(OSOBY.TITUL_ZA, person.getSuffixDegree())
                .addCol(OSOBY.DEGREE_SOURCE, ObjectUtil.getIdOrNull(person.getDegreeSource()))
                .addCol(OSOBY.GENDER, ObjectUtil.getIdOrNull(person.getGender()))
                .addCol(OSOBY.GENDER_SOURCE, ObjectUtil.getIdOrNull(person.getGenderSource()))
                .addCol(OSOBY.OPENID, person.getOpenidId())
                .addCol(OSOBY.OSOBA_GUID, person.getGuId())
                .addCol(OSOBY.NET_ID, person.getNetId())
                .addCol(OSOBY.BAKALARI, person.getBakalari())
                .addCol(OSOBY.SOL_ID, person.getSolId())
                .addCol(OSOBY.DAT_NAR, person.getBirthDate())
                .addCol(OSOBY.DEATH_DATE, person.getDeathDate())
                .addCol(OSOBY.LIFE_DATE_SOURCE, ObjectUtil.getIdOrNull(person.getLifeDateSource()))
                .addCol(OSOBY.ZAMESTNANI, person.getJob())
                .addCol(OSOBY.ADR_ZAM, person.getJobAddress())
                .addCol(OSOBY.VZDELANI, person.getEducationLevel())
                .addCol(OSOBY.TRIDA, person.getSchoolClass())
                .addCol(OSOBY.CISOP, person.getIdentityCardNumber())
                .addDateCol(OSOBY.MOJEID_POSL_UPDATE_TIME, person.getMojeIdLastUpdateDate(), true)
                .addCol(OSOBY.MOJEID_JE_VALID, person.isMojeIdValid());

        return List.of(tableWrite);
    }


    private List<TableWrite> generateWritesToInstitutionTable(Institution institution) {
        TableWrite tableWrite = TableWrite.createUpsert(INSTITUCE.TABLE)
                .addReferringWhereCol(INSTITUCE.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                .addCol(INSTITUCE.NAZEV, institution.getName())
                .addCol(INSTITUCE.ICO, institution.getIco())
                .addCol(INSTITUCE.DIC, institution.getDic())
                .addCol(INSTITUCE.HOMEPAGE_URL, institution.getHomepageUrl());

        if (institution instanceof Family) {
            tableWrite.addCol(INSTITUCE.TYP_INSTITUCE, InstitutionType.FAMILY.getId());
        }

        return List.of(tableWrite);
    }


    private List<TableWrite> generateWritesToLibraryTable(Library library) {
        return List.of(
                TableWrite.createUpsert(KNIHOVNY.TABLE)
                        .addReferringWhereCol(KNIHOVNY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                        .addCol(KNIHOVNY.SIGLA, library.getSigla()));
    }


    private List<TableWrite> generateWritesToSoftwareTable(Software software) {
        TableWrite tableWrite = TableWrite.createUpsert(SOFTWARE.TABLE)
                .addReferringWhereCol(SOFTWARE.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                .addCol(SOFTWARE.NAZEV, software.getName())
                .addCol(SOFTWARE.WEB_CRAWLER, software.getWebCrawler());
        return List.of(tableWrite);
    }

    private List<TableWrite> syncReaderAccounts(@NonNull User user, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        if (user.isEvided() && user.getReaderAccounts().isEmpty()) {
            // user has no reader accounts, so we compare the updated user with data in DB to check if we need to remove reader via appserver
            removeReaderAccountByReaderDeleterIfUserIsCurrentlyReader(user, currentDepartment, currentAuth);
            return List.of();
        }

        return user.getReaderAccounts()
                .stream()
                .map(r -> {
                    TableWrite tableWrite = TableWrite.createUpsert(CTENARI.TABLE)
                            .addReferringWhereCol(CTENARI.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                            .addCol(CTENARI.DAT_REG, r.getRegistrationDate())
                            .addCol(CTENARI.DATP_REG, r.getAccountCreationDate())
                            .addCol(CTENARI.KON_REG, r.getRegistrationExpirationDate().orElse(null))
                            .addCol(CTENARI.JE_BLOKOVAN, r.getBlocked())
                            .addCol(CTENARI.JE_POVOL, !r.getDeleted())
                            .addCol(CTENARI.FK_CTENKAT, r.getReaderCategory())
                            .addCol(CTENARI.FK_PUJC, r.getDepartment())
                            .addCol(CTENARI.KOD_STAVU, 0)
                            .addCol(CTENARI.TYP_TISK_REZE, r.getReservationsPrintType())
                            .addCol(CTENARI.TYP_TISK_UPOM, r.getOverdueNoticesPrintType())
                            .addCol(CTENARI.BAR_COD, r.getBarCode())
                            .addCol(CTENARI.CIS_LEG, r.getCardNumber())
                            .addCol(CTENARI.VZKAZ, r.getLibrarianMessage())
                            .addCol(CTENARI.POZNAMKA, r.getNote())
                            .addCol(CTENARI.RFID_UID, r.getRfid())
                            .addCol(CTENARI.FK_PUJC_ZMENA, currentDepartment.getId());

                    if (currentAuth.getActiveUser().equals(user)) {
                        return tableWrite
                                .addReferringCol(CTENARI.LAST_MODIFICATION_USER_ID, UZIVATELE.TABLE, UZIVATELE.ID_UZIV);
                    } else {
                        return tableWrite
                                .addCol(CTENARI.LAST_MODIFICATION_USER_ID, lastModificationUserAuthResolver.resolve(currentAuth));
                    }
                })
                .collect(Collectors.toList());
    }

    private void removeReaderAccountByReaderDeleterIfUserIsCurrentlyReader(@NonNull User user, @NonNull Department currentDepartment, @NonNull UserAuthentication currentAuth) {
        Assert.isTrue(user.isEvided(), "Can not remove reader account of user %s, because user is not evided.");

        var persistedUser = userLoader.getById(user.getId());

        if (!persistedUser.hasRole(ReaderRole.class)) {
            return;
        }

        readerDeleter.delete(new ReaderDeletionCommand(
                user,
                currentDepartment,
                currentAuth,
                false,
                true,
                true,
                true,
                true)); // only remove reader role and we only need to check debts
    }


    /**
     * soft deletes or upserts to editors table
     */
    private List<TableWrite> syncEditorAccounts(User user) {
        if (user.getEditorAccounts().isEmpty()) {
            TableWrite tableWrite = TableWrite.createUpdate(KNIHOVNICI.TABLE)
                    .addReferringWhereCol(KNIHOVNICI.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                    .addCol(KNIHOVNICI.JE_POVOL, false);
            return List.of(tableWrite);
        }
        return user.getEditorAccounts()
                .stream()
                .map(l -> TableWrite.createUpsert(KNIHOVNICI.TABLE)
                        .addReferringWhereCol(KNIHOVNICI.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                        .addCol(KNIHOVNICI.UROVEN, l.getEditLevel())
                        .addCol(KNIHOVNICI.FK_GROUP, l.getGroup() == null ? null : l.getGroup().getId())
                        .addCol(KNIHOVNICI.JE_POVOL, l.getActive())
                        .addCol(KNIHOVNICI.JE_SERVIS_WWW, l.getWithServicePrivileges())
                        .addCol(KNIHOVNICI.VAL_KOD, l.getValidationCode()))
                .collect(Collectors.toList());
    }


    /**
     * deletes or upserts to supplier table
     */
    private List<TableWrite> syncSupplierAccounts(User user) {
        TableWriteMode mode = !user.getSupplierAccounts().isEmpty() ? TableWriteMode.UPDATE_OR_INSERT : TableWriteMode.DELETE;
        TableWrite tableWrite = TableWrite.create(DODAVATELE.TABLE, mode)
                .addReferringWhereCol(DODAVATELE.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV);
        return List.of(tableWrite);
    }

    /**
     * deletes and reinserts all user contacts
     */
    private List<TableWrite> syncContacts(User user) {
        List<TableWrite> tableWrites = new ArrayList<>();

        tableWrites.add(
                TableWrite.createDelete(UZIV_KONTAKTY.TABLE)
                        .addReferringWhereCol(UZIV_KONTAKTY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV));

        final int[] order = {1};

        List<TableWrite> insertAllUserEmails = user.getContacts().stream()
                .map(contact -> {

                    // TEMPORARY NORMALIZE PHONE NUMBER - REMOVE AFTER CORRECT PHONE NUMBER VALIDATION IS ON FRONTEND
                    String value = contact.getValue();
                    ContactType type = contact.type();
                    if (type == ContactType.SMS_PHONE) {
                        if (!phoneNumberValidator.apply(value)) {
                            type = ContactType.PHONE;
                        } else {
                            value = phoneNumberValidator.toE164(value);
                        }
                    }
                    // END TEMPORARY NORMALIZE PHONE NUMBER

                    TableWrite tableWrite = TableWrite.createInsert(UZIV_KONTAKTY.TABLE)
                            .addReferringCol(UZIV_KONTAKTY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                            .addCol(UZIV_KONTAKTY.HODNOTA, value)
                            .addCol(UZIV_KONTAKTY.TYP, type.getId())
                            .addCol(UZIV_KONTAKTY.PORADI, order[0])
                            .addCol(UZIV_KONTAKTY.ZDROJ, contact.source().getId());
                    order[0]++;
                    return tableWrite;
                }).toList();
        tableWrites.addAll(insertAllUserEmails);

        if (user instanceof Person person) {
            List<TableWrite> idCardsTableWrite = person.getIdCards().stream()
                    .filter(c -> c instanceof IdCard)
                    .map(c -> (IdCard) c)
                    .map(idCard -> {
                        TableWrite tableWrite = TableWrite.createInsert(UZIV_KONTAKTY.TABLE)
                                .addReferringCol(UZIV_KONTAKTY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                                .addCol(UZIV_KONTAKTY.HODNOTA, idCard.getValue())
                                .addCol(UZIV_KONTAKTY.TYP, idCard.type().getId())
                                .addCol(UZIV_KONTAKTY.PORADI, order[0])
                                .addCol(UZIV_KONTAKTY.ZDROJ, idCard.source().getId())
                                .addCol(UZIV_KONTAKTY.VALIDITY_END_DATE, idCard.validityEndDate())
                                .addCol(UZIV_KONTAKTY.ISSUING_COUNTRY, ObjectUtil.elvis(idCard.issuingCountry(), Country::getAlpha2))
                                .addCol(UZIV_KONTAKTY.ID_CARD_ISSUER, idCard.issuer())
                                .addCol(UZIV_KONTAKTY.ID_CARD_ISSUE_DATE, idCard.issueDate());
                        order[0]++;
                        return tableWrite;
                    }).toList();
            tableWrites.addAll(idCardsTableWrite);

            List<TableWrite> paymentAccountsTableWrite = person.getPaymentAccounts().stream()
                    .filter(c -> c instanceof PaymentAccount)
                    .map(c -> (PaymentAccount) c)
                    .map(paymentAccount -> {
                        TableWrite tableWrite = TableWrite.createInsert(UZIV_KONTAKTY.TABLE)
                                .addReferringCol(UZIV_KONTAKTY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                                .addCol(UZIV_KONTAKTY.HODNOTA, paymentAccount.getValue())
                                .addCol(UZIV_KONTAKTY.TYP, paymentAccount.type().getId())
                                .addCol(UZIV_KONTAKTY.PORADI, order[0])
                                .addCol(UZIV_KONTAKTY.ZDROJ, paymentAccount.source().getId())
                                .addCol(UZIV_KONTAKTY.PAYMENT_ACCOUNT_CURRENCY_ID, paymentAccount.currency());
                        order[0]++;
                        return tableWrite;
                    }).toList();
            tableWrites.addAll(paymentAccountsTableWrite);

            List<TableWrite> birthNumberTableWrite = person.getBirthNumbers().stream()
                    .filter(c -> c instanceof BirthNumber)
                    .map(c -> (BirthNumber) c)
                    .map(birthNumber -> {
                        TableWrite tableWrite = TableWrite.createInsert(UZIV_KONTAKTY.TABLE)
                                .addReferringCol(UZIV_KONTAKTY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                                .addCol(UZIV_KONTAKTY.HODNOTA, birthNumber.getValue())
                                .addCol(UZIV_KONTAKTY.TYP, birthNumber.type().getId())
                                .addCol(UZIV_KONTAKTY.PORADI, order[0])
                                .addCol(UZIV_KONTAKTY.ZDROJ, birthNumber.source().getId());
                        order[0]++;
                        return tableWrite;
                    }).toList();
            tableWrites.addAll(birthNumberTableWrite);
        }

        return tableWrites;
    }


    /**
     * deletes and reinserts all user addresses
     */
    private List<TableWrite> syncUserAddresses(User user) {
        List<TableWrite> tableWrites = new ArrayList<>();

        if (user.isEvided()) {
            List<TableWrite> deleteAllUserAddresses = userAddressLoader.getAllByUserId(user.getId()).stream()
                    .flatMap(userAddress -> Stream.of(
                            TableWrite.createDelete(UZIV_ADRESY.TABLE).addWhereCol(UZIV_ADRESY.ID_UZIV_ADRESY, userAddress),
                            TableWrite.createDelete(ADRESY.TABLE).addWhereCol(ADRESY.ID_ADRESY, userAddress.address())
                    ))
                    .toList();
            tableWrites.addAll(deleteAllUserAddresses);
        }

        List<AddressCreateCommand> addressCreateCommands = user.getAddresses().stream()
                .map(UserAddress::address)
                .peek(address -> address.setId(null)) //we must null addresses ids to create new - not update (and so not to delete existing addresses in consequent write)
                .map(address -> new AddressCreateCommand(
                        address.getStreet(),
                        address.getCity(),
                        address.getPostalCode(),
                        address.getCountry()
                ))
                .toList();

        List<Address> savedAddresses = addressesCreator.create(addressCreateCommands);

        //nastavime adresam vygenerovana idecka
        for (int i = 0; i < user.getAddresses().size(); i++) {
            Address originalAddress = user.getAddresses().get(i).address();
            Address savedAddress = savedAddresses.get(i);
            originalAddress.setId(savedAddress.getId());
        }

        final int[] order = {1};
        List<TableWrite> insertUserAddresses = user.getAddresses().stream()
                .map(userAddress -> {
                    TableWrite tableWrite = TableWrite.createUpsert(UZIV_ADRESY.TABLE)
                            .addWhereCol(UZIV_ADRESY.ID_UZIV_ADRESY, userAddress)
                            .addReferringCol(UZIV_ADRESY.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                            .addCol(UZIV_ADRESY.FK_ADRESY, userAddress.address())
                            .addCol(UZIV_ADRESY.JE_TRV, userAddress.permanent())
                            .addCol(UZIV_ADRESY.JE_KOR, userAddress.mailing())
                            .addCol(UZIV_ADRESY.PORADI, order[0])
                            .addCol(UZIV_ADRESY.ZDROJ, userAddress.source());
                    order[0]++;
                    return tableWrite;
                })
                .toList();
        tableWrites.addAll(insertUserAddresses);

        return tableWrites;
    }

    private List<TableWrite> syncUserServiceProperties(User user) {
        Stream<TableWrite> deleteAllUserServiceProperties = Stream.of(
                TableWrite.createDelete(USER_SERVICE_PROPERTY.TABLE)
                        .addReferringWhereCol(USER_SERVICE_PROPERTY.USER_ID, UZIVATELE.TABLE, UZIVATELE.ID_UZIV));

        Stream<TableWrite> insertAllUserServiceProperties = user.getUserServiceProperties().stream()
                .map(userServiceProperty -> TableWrite.createInsert(USER_SERVICE_PROPERTY.TABLE)
                        .addReferringCol(USER_SERVICE_PROPERTY.USER_ID, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                        .addCol(USER_SERVICE_PROPERTY.ID, userServiceProperty.id())
                        .addCol(USER_SERVICE_PROPERTY.SERVICE, userServiceProperty.service())
                        .addCol(USER_SERVICE_PROPERTY.PROP_NAME, userServiceProperty.name())
                        .addCol(USER_SERVICE_PROPERTY.PROP_VALUE, userServiceProperty.value())
                        .addDateCol(USER_SERVICE_PROPERTY.VALIDITY_END_DATE, userServiceProperty.validityEndDate(), true));

        return concat(deleteAllUserServiceProperties, insertAllUserServiceProperties)
                .collect(Collectors.toList());
    }


    /**
     * deletes and reinserts all readable buildings
     */
    private List<TableWrite> syncReadableDepartments(User object) {
        Stream<TableWrite> deleteReadableDepartments = Stream.of(
                TableWrite.createDelete(VAZ_CTEN_PUJC.TABLE)
                        .addReferringWhereCol(VAZ_CTEN_PUJC.FK_UZIV_CTEN, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
        );

        Stream<TableWrite> insertReadableDepartments = object.getReadableDepartments().stream()
                .map(department ->
                        TableWrite.createInsert(VAZ_CTEN_PUJC.TABLE)
                                .addReferringCol(VAZ_CTEN_PUJC.FK_UZIV_CTEN, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                                .addCol(VAZ_CTEN_PUJC.FK_PUJC, department));

        return concat(deleteReadableDepartments, insertReadableDepartments).toList();
    }


    /**
     * deletes and reinserts all librarian buildings
     */
    private List<TableWrite> syncEditableDepartments(User object) {
        Stream<TableWrite> deleteEditableDepartments = Stream.of(
                TableWrite.createDelete(VAZ_UZIV_PUJC.TABLE)
                        .addReferringWhereCol(VAZ_UZIV_PUJC.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
        );

        Stream<TableWrite> insertEditableDepartments = ListUtil.emptyIfNull(object.getEditableDepartments()).stream()
                .map(department -> TableWrite.createInsert(VAZ_UZIV_PUJC.TABLE)
                        .addReferringCol(VAZ_UZIV_PUJC.FK_UZIV, UZIVATELE.TABLE, UZIVATELE.ID_UZIV)
                        .addCol(VAZ_UZIV_PUJC.FK_PUJC, department));

        return concat(deleteEditableDepartments, insertEditableDepartments).toList();
    }


    /**
     * deletes and reinserts all editable fonds
     */
    private List<TableWrite> syncEditableFonds(User user) {
        return List.of();
    }


    private List<TableWrite> syncSequences(User user) {
        if (!user.hasRole(ReaderRole.class) || !autogenerateUserCardNumberProvider.get()) {
            return List.of();
        }
        return user
                .roleStream(ReaderRole.class)
                .filter(req -> req.getCardNumber() != null)
                .flatMap(req -> {
                    Optional<CardNumberSequenceItem> nextOpt = cardNumberSequenceItemLoader.getNext(user.getReadableDepartments());
                    if (nextOpt.isEmpty()) {
                        return Stream.empty();
                    }
                    CardNumberSequenceItem next = nextOpt.get();
                    if (!next.isSame(req.getCardNumber())) {
                        return Stream.empty();
                    }
                    return cardNumberSequenceItemTableWriteGenerator.generateWrites(next).stream();
                })
                .toList();
    }


}
