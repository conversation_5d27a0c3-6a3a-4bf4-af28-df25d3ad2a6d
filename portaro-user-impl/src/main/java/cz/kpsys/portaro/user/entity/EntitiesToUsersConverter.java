package cz.kpsys.portaro.user.entity;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.ContactLoader;
import cz.kpsys.portaro.user.contact.UserAddress;
import cz.kpsys.portaro.user.contact.UserAddressLoader;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import cz.kpsys.portaro.user.prop.UserServicePropertyLoader;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.editor.LibrarianGroup;
import cz.kpsys.portaro.user.role.reader.PrintType;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderRoleImpl;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import cz.kpsys.portaro.user.sec.UserDepartmentsByUserIdsLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToUsersConverter implements Converter<List<? extends UserEntity>, List<User>> {

    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull ByIdLoadable<LibrarianGroup, Integer> librarianGroupLoader;
    @NonNull ContactLoader contactLoader;
    @NonNull UserAddressLoader userAddressLoader;
    @NonNull UserServicePropertyLoader userServicePropertyLoader;
    @NonNull UserDepartmentsByUserIdsLoader readableUserDepartmentsByUserIdsLoader;
    @NonNull UserDepartmentsByUserIdsLoader editableUserDepartmentsByUserIdsLoader;


    @Override
    public List<User> convert(@NonNull List<? extends UserEntity> entities) {
        List<Integer> userIds = ListUtil.convert(entities, UserEntity::id);
        Map<Integer, List<Department>> readableDepartmentsByUserMap = readableUserDepartmentsByUserIdsLoader.getAllByUserIds(userIds);
        Map<Integer, List<Department>> editableDepartmentsByUserMap = editableUserDepartmentsByUserIdsLoader.getAllByUserIds(userIds);
        Map<Integer, List<Contact>> contactsByUserMap = contactLoader.getAllByUserIds(userIds);
        Map<Integer, List<UserServiceProperty>>  userServicePropertyByUserMap = userServicePropertyLoader.getAllByUserIds(userIds);
        Map<Integer, List<UserAddress>> addressesByUserMap = userAddressLoader.getAllByUserIds(userIds);

        return ListUtil.convert(entities, entity -> {

            User user;
            if (entity.person() != null) {
                user = mapPerson(entity.person(), contactsByUserMap);
            } else if (entity.institution() != null) {
                user = mapInstitution(entity.institution(), entity.library());
            } else if (entity.software() != null) {
                user = mapSoftware(entity.software());
            } else {
                user = mapNoConcreteUser(entity);
            }

            if (entity.readerAccount() != null) {
                user.addUserRole(mapReaderAccount(entity.readerAccount()));
            }

            if (entity.editorAccount() != null) {
                user.addUserRole(mapEditorAccount(entity.editorAccount()));
            }

            if (entity.supplierAccount() != null) {
                user.addUserRole(SupplierRole.create(entity.supplierAccount().userId(), entity.supplierAccount().userId()));
            }

            user.setCreationEventId(entity.creationEventId());

            user.setActivationEventId(entity.activationEventId());

            user.setAnonymizationEventId(entity.anonymizationEventId());

            user.setDeletionEventId(entity.deletionEventId());

            user.setUsername(entity.username());

            if (entity.syncId() != null) {
                user.setSyncId(entity.syncId());
            }

            user.setRid(entity.rid());

            user.setRecordId(entity.recordId());

            user.setPasswordHash(entity.passwordHash());

            user.setReadableDepartments(ListUtil.notNullList(readableDepartmentsByUserMap.get(entity.id())));

            user.setEditableDepartments(ListUtil.notNullList(editableDepartmentsByUserMap.get(entity.id())));

            List<Contact> contacts = contactsByUserMap.get(entity.id());
            user.setEmails(new ArrayList<>(ListUtil.filter(contacts, object -> object.type().isEmail())));
            user.setPhoneNumbers(new ArrayList<>(ListUtil.filter(contacts, object -> object.type().isPhone())));

            user.setAddresses(new ArrayList<>(addressesByUserMap.get(entity.id())));

            user.setUserServiceProperties(new ArrayList<>(userServicePropertyByUserMap.get(entity.id())));

            return user;
        });
    }


    @NonNull
    private Person mapPerson(@NonNull PersonEntity entity, Map<Integer, List<Contact>> contactsByUserMap) {
        Person person = new Person(prettyUserNameGenerator, entity.userId());
        person.setFirstName(entity.firstName());
        person.setMiddleName(entity.middleName());
        person.setLastName(entity.lastName());
        person.setNameSource(entity.nameSource());
        person.setDegree(entity.degree());
        person.setSuffixDegree(entity.suffixDegree());
        person.setDegreeSource(entity.degreeSource());
        person.setGender(entity.gender());
        person.setGenderSource(entity.genderSource());
        person.setBirthDate(entity.birthDate());
        person.setDeathDate(entity.deathDate());
        person.setLifeDateSource(entity.lifeDateSource());
        person.setJob(entity.job());
        person.setJobAddress(entity.jobAddress());
        person.setEducationLevel(entity.educationLevel());
        person.setSchoolClass(entity.schoolClass());
        person.setIdentityCardNumber(entity.identityCardNumber());
        person.setBakalari(entity.bakalari());
        person.setSolId(entity.solId());
        person.setGuId(entity.guId());
        person.setNetId(entity.netId());
        person.setOpenidId(entity.openidId());
        person.setMojeIdValid(entity.mojeIdValid());
        person.setMojeIdLastUpdateDate(entity.mojeIdLastUpdateDate());
        List<Contact> contacts = contactsByUserMap.get(entity.userId());
        person.setIdCards(new ArrayList<>(ListUtil.filter(contacts, contact -> contact.type().isIdCard())));
        person.setPaymentAccounts(new ArrayList<>(ListUtil.filter(contacts, contact -> contact.type().isPaymentAccount())));
        person.setBirthNumbers(new ArrayList<>(ListUtil.filter(contacts, contact -> contact.type().isBirthNumber())));
        return person;
    }

    @NonNull
    private Institution mapInstitution(@NonNull InstitutionEntity institutionEntity, @Nullable LibraryEntity libraryEntity) {
        Institution inst;
        if (libraryEntity != null) {
            inst = new Library(
                    prettyUserNameGenerator,
                    libraryEntity.userId(),
                    institutionEntity.name(),
                    libraryEntity.sigla(),
                    libraryEntity.departmentId() == null ? null : departmentLoader.getById(libraryEntity.departmentId())
            );
        } else {
            inst = new Institution(
                    prettyUserNameGenerator,
                    institutionEntity.userId(),
                    institutionEntity.name(),
                    InstitutionType.CODEBOOK.getById(institutionEntity.institutionTypeId())
            );
        }
        inst.setIco(institutionEntity.ico());
        inst.setDic(institutionEntity.dic());
        inst.setHomepageUrl(institutionEntity.homepageUrl());
        return inst;
    }

    @NonNull
    private Software mapSoftware(@NonNull SoftwareEntity entity) {
        return new Software(
                entity.userId(),
                entity.name(),
                entity.webCrawler()
        );
    }

    @NonNull
    private User mapNoConcreteUser(@NonNull UserEntity entity) {
        return NoConcreteUser.notAnonymousUser(entity.id(), ObjectUtil.firstNotNull(entity.nativeName(), "UnspecifiedUser#%s".formatted(entity.id())));
    }

    @NonNull
    private ReaderRoleImpl mapReaderAccount(@NonNull ReaderAccountEntity entity) {
        return new ReaderRoleImpl(
                entity.userId(),
                entity.userId(),
                entity.cardNumber(),
                entity.barCode(),
                entity.registrationDate(),
                entity.firstRegistrationDate(),
                entity.registrationExpirationDate(),
                readerCategoryLoader.getById(entity.readerCategoryId()),
                departmentLoader.getById(entity.departmentId()),
                entity.deleted(),
                entity.blocked(),
                entity.note(),
                entity.librarianMessage(),
                entity.rfid(),
                PrintType.CODEBOOK.getById(entity.overdueNoticesPrintTypeId()),
                PrintType.CODEBOOK.getById(entity.reservationsPrintTypeId())
        );
    }

    @NonNull
    private EditorAccount mapEditorAccount(@NonNull EditorAccountEntity editorAccount) {
        return new EditorAccount(
                editorAccount.userId(),
                editorAccount.userId(),
                editorAccount.withServicePrivileges(),
                editorAccount.validationCode(),
                editorAccount.groupId() == null ? null : librarianGroupLoader.getById(editorAccount.groupId()),
                EditLevel.fromValue(editorAccount.editLevel()),
                editorAccount.active()
        );
    }
}
