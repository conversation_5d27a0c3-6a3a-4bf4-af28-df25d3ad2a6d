package cz.kpsys.portaro.user.edit.command;

import cz.kpsys.portaro.record.sec.UserEditableFondsLoader;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.contact.IdCardType;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.edit.EditableListConverter;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserEditationCommandConverter {

    @NonNull UserEditableFondsLoader editableUserFondsLoader;
    @NonNull EditableListConverter editableListConverter;

    public <UEC extends UserEditationCommand, USER extends User> UEC toCommand(USER user, SourceOfData sourceOfData) {
        UEC command = createObject(user);

        command.setId(user.getId());

        command.setActive(Optional.of(true));

        command.setUsername(Optional.ofNullable(user.getUsername()));

        command.setEmails(editableListConverter.convert(user.getEmails(), UserEmailEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData));

        command.setPhoneNumbers(editableListConverter.convert(user.getPhoneNumbers(), UserPhoneEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData));

        command.setAddresses(editableListConverter.convert(user.getAddresses(), UserAddressEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData));

        command.setReadableDepartments(editableListConverter.convert(user.getReadableDepartments(), DepartmentEditationMode.OVERWRITE_ALL));

        command.setEditableDepartments(editableListConverter.convert(user.getEditableDepartments(), DepartmentEditationMode.OVERWRITE_ALL));

        command.setEditableFonds(Optional.ofNullable(editableUserFondsLoader.getAllByUser(user)));

        command.setReaderAccounts(
                Optional.of(user.getUserRoles().stream()
                        .filter(role -> role instanceof ReaderRole)
                        .map(ReaderRole.class::cast)
                        .map(this::mapReaderAccountToCommand)
                        .toList()
                ));

        command.setEditorAccounts(Optional.of(
                user.getUserRoles().stream()
                        .filter(role -> role instanceof EditorAccount)
                        .map(EditorAccount.class::cast)
                        .map(this::mapEditorAccountToCommand)
                        .toList()
        ));

        command.setSupplierAccounts(Optional.of(
                user.getUserRoles().stream()
                        .filter(role -> role instanceof SupplierRole)
                        .map(SupplierRole.class::cast)
                        .map(this::mapSupplierAccountToCommand)
                        .toList()
        ));

        command.setUserServiceProperties(editableListConverter.convert(user.getUserServiceProperties(), UserServicePropertyCommandConverter::toCommand, UserServicePropertiesEditationMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SERVICE));

        if (command instanceof PersonEditationCommand) {
            fillPersonCommand((PersonEditationCommand) command, (Person) user, sourceOfData);
        }

        if (command instanceof InstitutionEditationCommand) {
            fillInstitutionCommand((InstitutionEditationCommand) command, (Institution) user);
        }

        if (command instanceof LibraryEditationCommand) {
            fillLibraryCommand((LibraryEditationCommand) command, (Library) user);
        }

        if (command instanceof SoftwareEditationCommand) {
            fillSoftwareCommand((SoftwareEditationCommand) command, (Software) user);
        }

        return command;
    }

    public <UEC extends UserEditationCommand, USER extends User> UEC createObject(USER user) {
        return switch (user) {
            case Family _ -> (UEC) new FamilyEditationCommand();
            case Library _ -> (UEC) new LibraryEditationCommand();
            case Institution _ -> (UEC) new InstitutionEditationCommand();
            case Software _ -> (UEC) new SoftwareEditationCommand();
            case Person _ -> (UEC) new PersonEditationCommand();
            default -> (UEC) new UserEditationCommand();
        };
    }

    private void fillPersonCommand(PersonEditationCommand command, Person person, SourceOfData sourceOfData) {
        command.setBakalariId(Optional.ofNullable(person.getBakalari()));
        command.setSolId(Optional.ofNullable(person.getSolId()));
        command.setSyncId(Optional.ofNullable(person.getSyncId()));
        command.setBirthDate(Optional.ofNullable(person.getBirthDate()));
        command.setDeathDate(Optional.ofNullable(person.getDeathDate()));
        command.setLifeDateSource(Optional.ofNullable(person.getLifeDateSource()));
        command.setPrefixDegree(Optional.ofNullable(person.getDegree()));
        command.setDegreeSource(Optional.ofNullable(person.getDegreeSource()));
        command.setEducationLevel(Optional.ofNullable(person.getEducationLevel()));
        command.setFirstName(Optional.ofNullable(person.getFirstName()));
        command.setGuId(Optional.ofNullable(person.getGuId()));
        command.setIdentityCardNumber(Optional.ofNullable(person.getIdentityCardNumber()));
        command.setJobName(Optional.ofNullable(person.getJob()));
        command.setJobAddress(Optional.ofNullable(person.getJobAddress()));
        command.setMiddleName(Optional.ofNullable(person.getMiddleName()));
        command.setLastName(Optional.ofNullable(person.getLastName()));
        command.setNameSource(Optional.ofNullable(person.getNameSource()));
        command.setGender(Optional.ofNullable(person.getGender()));
        command.setGenderSource(Optional.ofNullable(person.getGenderSource()));
        command.setMojeIdLastUpdateDate(Optional.ofNullable(person.getMojeIdLastUpdateDate()));
        command.setMojeIdValid(Optional.of(person.isMojeIdValid()));
        command.setNetId(Optional.ofNullable(person.getNetId()));
        command.setOpenidId(Optional.ofNullable(person.getOpenidId()));
        command.setSchoolClass(Optional.ofNullable(person.getSchoolClass()));
        command.setSuffixDegree(Optional.ofNullable(person.getSuffixDegree()));
        command.setIdCards(editableListConverter.convert(person.getIdCards(), PersonIdCardEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData, List.of(IdCardType.values())));
        command.setPaymentAccounts(editableListConverter.convert(person.getPaymentAccounts(), PersonPaymentAccountEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData));
        command.setBirthNumbers(editableListConverter.convert(person.getBirthNumbers(), PersonBirthNumberEditationCommandConverter::toCommand, SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, sourceOfData));
    }

    private void fillInstitutionCommand(InstitutionEditationCommand request, Institution institution) {
        request.setInstitutionName(Optional.ofNullable(institution.getName()));
        request.setInstitutionIco(Optional.ofNullable(institution.getIco()));
        request.setInstitutionDic(Optional.ofNullable(institution.getDic()));
        request.setHomepageUrl(Optional.ofNullable(institution.getHomepageUrl()));
    }

    private void fillLibraryCommand(LibraryEditationCommand request, Library library) {
        request.setLibrarySigla(Optional.ofNullable(library.getSigla()));
    }

    private void fillSoftwareCommand(SoftwareEditationCommand request, Software software) {
        request.setName(Optional.ofNullable(software.getName()));
        request.setWebCrawler(Optional.of(software.getWebCrawler()));
    }

    private ReaderAccountEditationCommand mapReaderAccountToCommand(ReaderRole role) {
        return ReaderAccountEditationCommand.create(
                Optional.ofNullable(role.getDeleted()),
                Optional.ofNullable(role.getBlocked()),
                Optional.ofNullable(role.getReaderCategory()),
                Optional.ofNullable(role.getDepartment()),
                Optional.ofNullable(role.getReservationsPrintType()),
                Optional.ofNullable(role.getOverdueNoticesPrintType()),
                Optional.ofNullable(role.getCardNumber()),
                Optional.ofNullable(role.getBarCode()),
                Optional.ofNullable(role.getNote()),
                Optional.ofNullable(role.getRfid()),
                Optional.ofNullable(role.getRegistrationDate()),
                role.getRegistrationExpirationDate(),
                Optional.ofNullable(role.getLibrarianMessage())
        );
    }

    public EditorAccountEditationCommand mapEditorAccountToCommand(EditorAccount editorAccount) {
        return EditorAccountEditationCommand.create(
                Optional.ofNullable(editorAccount.getWithServicePrivileges()),
                Optional.ofNullable(editorAccount.getValidationCode()),
                Optional.ofNullable(editorAccount.getGroup()),
                Optional.ofNullable(editorAccount.getEditLevel()),
                Optional.ofNullable(editorAccount.getActive())
        );
    }

    private SupplierAccountEditationCommand mapSupplierAccountToCommand(SupplierRole s) {
        return SupplierAccountEditationCommand.ofEmpty();
    }
}
