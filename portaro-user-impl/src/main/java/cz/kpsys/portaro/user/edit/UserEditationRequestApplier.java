package cz.kpsys.portaro.user.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.contact.*;
import cz.kpsys.portaro.user.edit.applier.DefaultebleSetValueApplier;
import cz.kpsys.portaro.user.edit.applier.InputableAndDefaultableSetValueApplier;
import cz.kpsys.portaro.user.edit.applier.SimpleSetValueApplier;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import cz.kpsys.portaro.user.prop.UserServicePropertyCommand;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.editor.LibrarianGroup;
import cz.kpsys.portaro.user.role.reader.PrintType;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.user.role.reader.ReaderRoleImpl;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@SuppressWarnings("OptionalAssignedToNull")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserEditationRequestApplier {

    @NonNull PasswordEncoder passwordEncoder;
    @NonNull ContextualProvider<Department, BarCodeValidator> userBarCodeValidatorProvider;
    @NonNull UserStringGenerator prettyUserNameGenerator;

    // User appliers
    @NonNull SimpleSetValueApplier<User, Integer, Integer> idSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, User, UUID, UUID> newIdSetValueApplier;
    @NonNull SimpleSetValueApplier<User, Boolean, UUID> activeSetValueApplier;
    @NonNull SimpleSetValueApplier<User, String, String> usernameSetValueApplier;
    @NonNull SimpleSetValueApplier<User, String, String> syncIdSetValueApplier;
    @NonNull SimpleSetValueApplier<User, SourcedEditableList<UserEmailEditationCommand>, List<Contact>> emailSetValueApplier;
    @NonNull SimpleSetValueApplier<User, SourcedEditableList<PhoneNumberEditationCommand>, List<Contact>> phoneNumberSetValueApplier;
    @NonNull SimpleSetValueApplier<User, SourcedEditableList<UserAddressEditationCommand>, List<UserAddress>> addressSetValueApplier;
    @NonNull SimpleSetValueApplier<User, SimpleEditableList<UserServicePropertyCommand, UserServicePropertiesEditationMode>, List<UserServiceProperty>> userServicePropertiesSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<SimpleUserEditationInput, User, SimpleEditableList<Department, DepartmentEditationMode>, List<Department>> readableDepartmentsSetValueApplier;
    @NonNull SimpleSetValueApplier<User, UUID, UUID> recordIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> firstNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> middleNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> lastNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourceOfData, SourceOfData> nameSourceSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> prefixDegreeSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> suffixDegreeSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourceOfData, SourceOfData> degreeSourceSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, Gender, Gender> genderSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourceOfData, SourceOfData> genderSourceSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, LocalDate, LocalDate> birthDateSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, LocalDate, LocalDate> deathDateSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourceOfData, SourceOfData> lifeDateSourceSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> guIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> netIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> openidIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> bakalariIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> solIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> personSyncIdSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> jobNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> jobAddressSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> educationLevelSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> schoolClassSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, String, String> identityCardNumberSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, Boolean, Boolean> mojeIdValidSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, Date, Date> mojeIdLastUpdateDateSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourcedAndTypedEditableList<PersonIdCardEditationCommand, IdCardType>, List<Contact>> idCardsSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourcedEditableList<PersonPaymentAccountEditationCommand>, List<Contact>> paymentAccountsSetValueApplier;
    @NonNull SimpleSetValueApplier<Person, SourcedEditableList<PersonBirthNumberEditationCommand>, List<Contact>> birthNumbersSetValueApplier;
    @NonNull SimpleSetValueApplier<Institution, String, String> institutionNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Institution, String, String> institutionIcoSetValueApplier;
    @NonNull SimpleSetValueApplier<Institution, String, String> institutionDicSetValueApplier;
    @NonNull SimpleSetValueApplier<Institution, String, String> institutionHomepageUrlSetValueApplier;
    @NonNull SimpleSetValueApplier<Family, String, String> familyNameSetValueApplier;
    @NonNull SimpleSetValueApplier<Library, String, String> librarySiglaSetValueApplier;
    @NonNull SimpleSetValueApplier<Software, String, String> softwareNameSetValueApplier;
    @NonNull DefaultebleSetValueApplier<Software, Boolean, Boolean> webCrawlerSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, Department, Department> originalDepartmentSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, String, String> cardNumberSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, String, String> barcodeSetValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, LocalDate, LocalDate> registrationDateValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, LocalDate, LocalDate> registrationExpirationDateValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, ReaderCategory, ReaderCategory> readerCategoryValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, Boolean, Boolean> readerCategoryDeletedValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, Boolean, Boolean> readerCategoryBlockedValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, PrintType, PrintType> readerOverdueNoticesPrintTypeValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, ReaderRole, PrintType, PrintType> readerReservationsPrintTypeValueApplier;
    @NonNull SimpleSetValueApplier<ReaderRole, String, String> readerNotesValueApplier;
    @NonNull SimpleSetValueApplier<ReaderRole, String, String> readerLibrarianMessageValueApplier;
    @NonNull SimpleSetValueApplier<ReaderRole, String, String> readerRfidValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<SimpleUserEditationInput, EditorAccount, LibrarianGroup, LibrarianGroup> groupValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<SimpleUserEditationInput, EditorAccount, Boolean, Boolean> withServicePrivilegesValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<SimpleUserEditationInput, EditorAccount, Boolean, Boolean> editorAccountActiveValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<SimpleUserEditationInput, EditorAccount, EditLevel, EditLevel> editLevelValueApplier;
    @NonNull SimpleSetValueApplier<EditorAccount, String, String> validationCodeValueApplier;
    @NonNull InputableAndDefaultableSetValueApplier<UserEditationInput, User, SimpleEditableList<Department, DepartmentEditationMode>, List<Department>> editableDepartmentsSetValueApplier;


    public @NonNull <USER extends User, UEC extends UserEditationCommand> UserModificationResult<USER> applyRequest(@NonNull User user, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @Valid UEC command) {
        boolean modified = false;
        Optional<Boolean> activation = Optional.empty();

        if (command instanceof PersonEditationCommand) {
            if (!(user instanceof Person)) {
                Assert.isInstanceOf(NoConcreteUser.class, user);
                user = concretizeToPerson((NoConcreteUser) user);
            }
            Assert.isInstanceOf(Person.class, user);
        }

        if (command instanceof LibraryEditationCommand) {
            if (!(user instanceof Library)) {
                Assert.isInstanceOf(NoConcreteUser.class, user);
                user = concretizeToLibrary((NoConcreteUser) user);
            }
            Assert.isInstanceOf(Library.class, user);
        }

        if (command instanceof InstitutionEditationCommand) {
            Assert.isInstanceOf(Institution.class, user);
        }

        if (command instanceof FamilyEditationCommand) {
            Assert.isInstanceOf(Family.class, user);
        }

        final User finalUser = user;

        SimpleUserEditationInput simpleInput = new SimpleUserEditationInput(ctx, currentAuth);
        modified |= readableDepartmentsSetValueApplier.apply(simpleInput, user, command.getReadableDepartments());
        if (command.getEditorAccounts() != null && command.getEditorAccounts().isPresent()) {
            modified |= removeAbsentEditorAccounts(finalUser, command.getEditorAccounts().get());
            modified |= command.getEditorAccounts().get().stream().anyMatch(editorRequest -> modifyOrAddEditorAccount(simpleInput, finalUser, editorRequest, ctx));
        }

        UserEditationInput input = new UserEditationInput(ctx, currentAuth, user.getReadableDepartments(), user instanceof Library, user.getEditorAccounts());

        modified |= idSetValueApplier.apply(user, objectToNullableOptional(command.getId()));
        if (activeSetValueApplier.apply(user, command.getActive())) {
            modified = true;
            activation = Optional.of(user.getActivationEventId() != null);
        }

        if (command.getNewPassword() != null && command.getNewPassword().isPresent()) {
            user.setPasswordHash(passwordEncoder.encode(command.getNewPassword().orElse(null)));
            modified = true;
        }

        modified |= usernameSetValueApplier.apply(user, command.getUsername());
        modified |= syncIdSetValueApplier.apply(user, command.getSyncId());
        modified |= emailSetValueApplier.apply(user, command.getEmails());
        modified |= phoneNumberSetValueApplier.apply(user, command.getPhoneNumbers());
        modified |= addressSetValueApplier.apply(user, command.getAddresses());
        modified |= userServicePropertiesSetValueApplier.apply(user, command.getUserServiceProperties());
        modified |= newIdSetValueApplier.apply(input, user, command.getRid());
        modified |= recordIdSetValueApplier.apply(user, command.getRecordId());

        if (user instanceof Person person) {
            Assert.isInstanceOf(PersonEditationCommand.class, command);
            modified |= fillPerson(person, (PersonEditationCommand) command);
        }

        if (user instanceof Institution institution) {
            Assert.isInstanceOf(InstitutionEditationCommand.class, command);
            modified |= fillInstitution(institution, (InstitutionEditationCommand) command);
        }

        if (user instanceof Family family) {
            Assert.isInstanceOf(FamilyEditationCommand.class, command);
            modified |= fillFamily(family, (FamilyEditationCommand) command);
        }

        if (user instanceof Library library) {
            Assert.isInstanceOf(LibraryEditationCommand.class, command);
            modified |= fillLibrary(library, (LibraryEditationCommand) command);
        }

        if (user instanceof Software software) {
            Assert.isInstanceOf(SoftwareEditationCommand.class, command);
            modified |= fillSoftware(software, (SoftwareEditationCommand) command);
        }

        if (command.getSupplierAccounts() != null && command.getSupplierAccounts().isPresent()) {
            modified |= command.getSupplierAccounts().get().stream().anyMatch(supplierAccount -> modifyOrAddSupplierAccount(finalUser, supplierAccount));
        }

        if (command.getReaderAccounts() != null && command.getReaderAccounts().isPresent()) {
            modified |= removeAbsentReaderAccounts(finalUser, command.getReaderAccounts().get());
            modified |= command.getReaderAccounts().get().stream().anyMatch(readerRequest -> modifyOrAddReaderAccount(input, finalUser, command.getId(), readerRequest, ctx, currentAuth));
        }

        modified |= editableDepartmentsSetValueApplier.apply(input, user, command.getEditableDepartments());

        return new UserModificationResult<>((USER) user, modified, activation);
    }

    private Person concretizeToPerson(@NonNull NoConcreteUser user) {
        Person person = new Person(prettyUserNameGenerator, user.getId());
        person.setRecordId(user.getRecordId());
        user.getUserRoles().forEach(person::addUserRole);
        person.setReadableDepartments(user.getReadableDepartments());
        person.setEditableDepartments(user.getEditableDepartments());
        return person;
    }

    private Library concretizeToLibrary(@NonNull NoConcreteUser user) {
        Library library = new Library(prettyUserNameGenerator, user.getId(), null, null, null);
        library.setRecordId(user.getRecordId());
        user.getUserRoles().forEach(library::addUserRole);
        library.setReadableDepartments(user.getReadableDepartments());
        library.setEditableDepartments(user.getEditableDepartments());
        return library;
    }

    private boolean fillPerson(Person person, PersonEditationCommand request) {
        boolean modified = false;
        modified |= firstNameSetValueApplier.apply(person, request.getFirstName());
        modified |= middleNameSetValueApplier.apply(person, request.getMiddleName());
        modified |= lastNameSetValueApplier.apply(person, request.getLastName());
        modified |= nameSourceSetValueApplier.apply(person, request.getNameSource());
        modified |= prefixDegreeSetValueApplier.apply(person, request.getPrefixDegree());
        modified |= suffixDegreeSetValueApplier.apply(person, request.getSuffixDegree());
        modified |= degreeSourceSetValueApplier.apply(person, request.getDegreeSource());
        modified |= genderSetValueApplier.apply(person, request.getGender());
        modified |= genderSourceSetValueApplier.apply(person, request.getGenderSource());
        modified |= birthDateSetValueApplier.apply(person, request.getBirthDate());
        modified |= deathDateSetValueApplier.apply(person, request.getDeathDate());
        modified |= lifeDateSourceSetValueApplier.apply(person, request.getLifeDateSource());
        modified |= guIdSetValueApplier.apply(person, request.getGuId());
        modified |= netIdSetValueApplier.apply(person, request.getNetId());
        modified |= openidIdSetValueApplier.apply(person, request.getOpenidId());
        modified |= bakalariIdSetValueApplier.apply(person, request.getBakalariId());
        modified |= solIdSetValueApplier.apply(person, request.getSolId());
        modified |= personSyncIdSetValueApplier.apply(person, request.getSyncId());
        modified |= jobNameSetValueApplier.apply(person, request.getJobName());
        modified |= jobAddressSetValueApplier.apply(person, request.getJobAddress());
        modified |= educationLevelSetValueApplier.apply(person, request.getEducationLevel());
        modified |= schoolClassSetValueApplier.apply(person, request.getSchoolClass());
        modified |= identityCardNumberSetValueApplier.apply(person, request.getIdentityCardNumber());
        modified |= mojeIdValidSetValueApplier.apply(person, request.getMojeIdValid());

        if (request.getMojeIdLastUpdateDate() != null) {
            if ((person.getMojeIdLastUpdateDate() == null || (request.getMojeIdLastUpdateDate().isPresent() && person.getMojeIdLastUpdateDate().before(request.getMojeIdLastUpdateDate().get())))) {
                // everytime set newer value
                modified |= mojeIdLastUpdateDateSetValueApplier.apply(person, request.getMojeIdLastUpdateDate());
            }
        }

        modified |= idCardsSetValueApplier.apply(person, request.getIdCards());
        modified |= paymentAccountsSetValueApplier.apply(person, request.getPaymentAccounts());
        modified |= birthNumbersSetValueApplier.apply(person, request.getBirthNumbers());

        return modified;
    }

    private boolean fillInstitution(Institution institution, InstitutionEditationCommand request) {
        boolean modified = false;
        modified |= institutionNameSetValueApplier.apply(institution, request.getInstitutionName());
        modified |= institutionIcoSetValueApplier.apply(institution, request.getInstitutionIco());
        modified |= institutionDicSetValueApplier.apply(institution, request.getInstitutionDic());
        modified |= institutionHomepageUrlSetValueApplier.apply(institution, request.getHomepageUrl());
        return modified;
    }

    private boolean fillFamily(Family family, FamilyEditationCommand request) {
        return familyNameSetValueApplier.apply(family, request.getInstitutionName());
    }

    private boolean fillLibrary(Library library, LibraryEditationCommand request) {
        return librarySiglaSetValueApplier.apply(library, request.getLibrarySigla());
    }

    private boolean fillSoftware(Software software, SoftwareEditationCommand request) {
        boolean modified = false;
        modified |= softwareNameSetValueApplier.apply(software, request.getName());
        modified |= webCrawlerSetValueApplier.apply(software, request.getWebCrawler());
        return modified;
    }

    private boolean removeAbsentReaderAccounts(@NonNull User user, List<ReaderAccountEditationCommand> requests) {
        var removedAccounts = user.roleStream(ReaderRole.class)
                .filter(_ -> requests.isEmpty()) // TODO: when ReaderRole will have own id, change it to account -> !requests.map(::getId).toList().contains(account.getId())
                .toList();

        removedAccounts.forEach(user::removeUserRole);
        return !removedAccounts.isEmpty();
    }

    private boolean modifyOrAddReaderAccount(@NonNull UserEditationInput input, @NonNull User user, Integer userId, ReaderAccountEditationCommand request, Department ctx, UserAuthentication currentAuth) {
        boolean modified = false;

        Optional<ReaderRole> existingAccount = user.roleStream(ReaderRole.class)
                .filter(account -> true) // TODO: when readerRole will have own id, change it to account -> account.getId().equals(request.getId())
                .findFirst();

        ReaderRole reader = existingAccount.orElseGet(() -> ReaderRoleImpl.create(userId, userId));

        modified |= originalDepartmentSetValueApplier.apply(input, reader, request.getOriginDepartment());
        modified |= cardNumberSetValueApplier.apply(input, reader, request.getCardNumber());

        Optional<String> optionalBarcode;
        if (request.getBarCode() != null) {
            optionalBarcode = request.getBarCode().map(barcode -> userBarCodeValidatorProvider.getOn(ctx).getCore(barcode));
        } else {
            optionalBarcode = null;
        }
        modified |= barcodeSetValueApplier.apply(input, reader, optionalBarcode);
        modified |= registrationDateValueApplier.apply(input, reader, request.getRegistrationDate());

        if (reader.getAccountCreationDate() == null) {
            reader.setAccountCreationDate(ObjectUtil.firstNotNull(reader.getRegistrationDate(), LocalDate.now()));
            modified = true;
        }

        modified |= registrationExpirationDateValueApplier.apply(input, reader, request.getRegistrationExpirationDate());
        modified |= readerCategoryValueApplier.apply(input, reader, request.getReaderCategory());
        modified |= readerCategoryDeletedValueApplier.apply(input, reader, request.getDeleted());
        modified |= readerCategoryBlockedValueApplier.apply(input, reader, request.getBlocked());
        modified |= readerOverdueNoticesPrintTypeValueApplier.apply(input, reader, request.getOverdueNoticesPrintType());
        modified |= readerReservationsPrintTypeValueApplier.apply(input, reader, request.getReservationsPrintType());
        modified |= readerNotesValueApplier.apply(reader, request.getNote());
        modified |= readerLibrarianMessageValueApplier.apply(reader, request.getLibrarianMessage());
        modified |= readerRfidValueApplier.apply(reader, request.getRfid());

        if (existingAccount.isEmpty()) {
            user.addUserRole(reader);
            modified = true;
        }
        return modified;
    }

    private boolean removeAbsentEditorAccounts(@NonNull User user, List<EditorAccountEditationCommand> requests) {
        var removedAccounts = user.roleStream(EditorAccount.class)
                .filter(account -> requests.isEmpty()) // TODO: when EditorAccount will have own id, change it to account -> !requests.map(::getId).toList().contains(account.getId())
                .toList();

        removedAccounts.forEach(user::removeUserRole);
        return !removedAccounts.isEmpty();
    }

    private boolean modifyOrAddEditorAccount(@NonNull SimpleUserEditationInput input, @NonNull User user, EditorAccountEditationCommand request, Department ctx) {
        boolean modified = false;

        Optional<EditorAccount> existingAccount = user.roleStream(EditorAccount.class)
                .filter(account -> true) // TODO: when EditorAccount will have own id, change it to account -> account.getId().equals(request.getId())
                .findFirst();

        EditorAccount editor = existingAccount.orElseGet(() -> EditorAccount.ofEmpty(user.getId(), user.getId()));

        modified |= groupValueApplier.apply(input, editor, request.getGroup());
        modified |= withServicePrivilegesValueApplier.apply(input, editor, request.getWithServicePrivileges());
        modified |= editorAccountActiveValueApplier.apply(input, editor, request.getActive());
        modified |= editLevelValueApplier.apply(input, editor, request.getEditLevel());
        modified |= validationCodeValueApplier.apply(editor, request.getValidationCode());

        if (existingAccount.isEmpty()) {
            user.addUserRole(editor);
            modified = true;
        }
        return modified;
    }

    private boolean modifyOrAddSupplierAccount(@NonNull User user, SupplierAccountEditationCommand request) {
        Optional<SupplierRole> existingAccount = user.roleStream(SupplierRole.class)
                .filter(account -> true) // TODO: when supplierRole will have own id, change it to account -> account.getId().equals(request.getId())
                .findFirst();

        if (existingAccount.isEmpty()) {
            SupplierRole newAccount = SupplierRole.create(user.getId(), user.getId());
            user.addUserRole(newAccount);
            return true;
        }
        return false;
    }

    private static <V> @Nullable Optional<V> objectToNullableOptional(V data) {
        return data == null ? null : Optional.of(data);
    }
}
