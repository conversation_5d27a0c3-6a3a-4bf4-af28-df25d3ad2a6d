package cz.kpsys.portaro.user.edit.command;

import cz.kpsys.portaro.record.edit.RecordEditation;
import lombok.NonNull;
import lombok.With;
import org.jspecify.annotations.Nullable;

import java.util.List;

@With
public record UserRecordEditationCommand<UEC extends UserEditationCommand>(

        @NonNull UEC userEditationCommand,

        @Nullable RecordEditation recordEditation,

        @NonNull List<RecordEditation> dependentRecordEditations
) {
    public UserRecordEditationCommand(@NonNull UEC userEditationCommand, @Nullable RecordEditation recordEditation, @NonNull List<RecordEditation> dependentRecordEditations) {
        this.userEditationCommand = userEditationCommand;
        this.recordEditation = recordEditation;
        this.dependentRecordEditations = dependentRecordEditations;

        if (recordEditation != null) {
            userEditationCommand.setRecord(recordEditation.getRecord());
        }
    }

    public static <UEC extends UserEditationCommand> UserRecordEditationCommand<UEC> ofUserEditationOnly(UEC userEditationCommand) {
        return new UserRecordEditationCommand<>(userEditationCommand, null, List.of());
    }
}
