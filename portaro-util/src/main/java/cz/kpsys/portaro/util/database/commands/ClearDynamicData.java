package cz.kpsys.portaro.util.database.commands;

import cz.kpsys.portaro.appserver.config.AppserverConfigService;
import cz.kpsys.portaro.appserver.config.AppserverCustomSettingsRefresher;
import cz.kpsys.portaro.commons.concurrent.ExecutorServiceHelper;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.databasestructure.*;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.deletion.RecordHardDeleter;
import cz.kpsys.portaro.setting.RelogUserProcedureTransactionAuthenticator;
import cz.kpsys.portaro.sql.generator.Query;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SqlDataClearHelper;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ClearDynamicData {

    public static final List<String> FIREBIRD_ZAL_TABLES = List.of(
            "zal_akce",
            "zal_autfond",
            "zal_autindik",
            "zal_budova",
            "zal_cten_cisleg",
            "zal_ctenkat",
            "zal_ctenvyp",
            "zal_dokfond",
            "zal_dokindik",
            "zal_duvubyt",
            "zal_el_sluzby",
            "zal_fdef",
            "zal_fdefaut",
            "zal_fond",
            "zal_fraze",
            "zal_gridedit_form",
            "zal_gridedit_grid",
            "zal_gridedit_val",
            "zal_group",
            "zal_hled",
            "zal_hledradek",
            "zal_hotkey_map",
            "zal_int_pc",
            "zal_klislov",
            "zal_kody007",
            "zal_kody008",
            "zal_kultvzdel",
            "zal_lic_db",
            "zal_lok_tema",
            "zal_lokace",
            "zal_meny",
            "zal_metodika",
            "zal_pol_typfak",
            "zal_popl",
            "zal_prircis_rady",
            "zal_pujc",
            "zal_pujc_cisleg",
            "zal_pujc_lok",
            "zal_sigla",
            "zal_sign_rady",
            "zal_status",
            "zal_stavmvs",
            "zal_stavz",
            "zal_styly",
            "zal_temskup",
            "zal_tisksest",
            "zal_trideni",
            "zal_typpopl",
            "zal_typpujc",
            "zal_typtisksest",
            "zal_ucty",
            "zal_uziv",
            "zal_val",
            "zal_vyd_sluzby",
            "zal_vykony",
            "zal_vylslov",
            "zal_vypakce",
            "zal_vypkat",
            "zal_zaut_typy",
            "zal_zdok_typy",
            "zal_zpnab",
            "zal_zpnahr"
    );
    @NonNull ExecutorServiceHelper executorServiceHelper;
    @NonNull Provider<User> suUserProvider;
    @NonNull AdhocQueryer<Object> adhocQueryer;
    @NonNull DatabaseProperties dbUpdateDatabaseProperties;
    @NonNull AppserverConfigService appserverConfigService;
    @NonNull AppserverCustomSettingsRefresher appserverCustomSettingsRefresher;
    @NonNull List<String> protectedUsernames;
    @NonNull SqlDataClearHelper sqlDataClearHelper;

    public void call(@NonNull ClearDynamicDataParameters parameters) {
        executorServiceHelper.awaitCompletion(Duration.ofSeconds(15));
        appserverConfigService.disableIndexing();
        appserverConfigService.clearIndex();
        appserverCustomSettingsRefresher.clearCache();

        try {
            adhocQueryer.update(Void.TYPE, (jdbc) -> {
                List<Long> protectedUserIds = jdbc.queryForList("select id_uziv from uzivatele where username in (:usernames)", Map.of("usernames", protectedUsernames), Long.class);

                String subselect1 = "select fk_fulltext_skupina from opac_custom_dirs where fk_pujc = (select id_pujc from def_pujc where fk_nadr is null)";
                String subselect2 = "select fk_fulltext_skupiny from kat1_4 where id_zaz = 0";
                List<Long> protectedFulltextGroupIds = jdbc.queryForList("select id_fulltext_skupiny from fulltext_skupiny where fk_nadr in (%s) or id_fulltext_skupiny in (%s) or id_fulltext_skupiny in (%s)".formatted(subselect1, subselect1, subselect2), Map.of(), Long.class);

                List<String> protectedRecordIds = jdbc.queryForList("select record_id from KAT1_4 where id_zaz = 0", Map.of(), String.class);
                protectedRecordIds.add(RecordConstants.MOCK_RECORD_ID_STRING);

                QueryFactory qf = sqlDataClearHelper.getQueryFactoryByDatabaseType();

                List<Query> queries = new ArrayList<>();

                if (dbUpdateDatabaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)) {
                    new RelogUserProcedureTransactionAuthenticator(jdbc.getJdbcTemplate(), () -> suUserProvider.get().getId()).authenticate();
                }

                if (parameters.includingIni() != null && parameters.includingIni()) {
                    queries.add(sqlDataClearHelper.deleteTable(qf, SettingDb.LogIniChange.TABLE));
                    queries.add(sqlDataClearHelper.deleteTable(qf, SettingDb.INI_FILE.TABLE));
                }
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_DEBUG_VYPUC.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_VYPUC.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.VYPUC_REZE_PUJCOVNY.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.VYPUC_TIMESLOT.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.VYPUC_TITPER.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.POZ_REZ.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, ReminderDb.UPOMINKY.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, ReminderDb.UPOMINKY2.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.VYPUC.TABLE_ACTIVE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoanDb.VYPUC.TABLE_ALL));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoggingDb.ACTIONS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.OPAC_RATING.OPAC_RATING));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.OPAC_HODNOCENI.OPAC_HODNOCENI));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_CTEN.TABLE));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.UZIV_ADRESY.TABLE, UserDb.UZIV_ADRESY.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTable(qf, UserDb.ADRESY.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, LoggingDb.SESSIONS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.KARANTENA.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordOperationDb.RECORD_OPERATION.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_LINK.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.POL_EXPED.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, AcquisitionDb.POL_FAK.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.HL_FAK.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, ExemplarDb.UBYTKY.UBYTKY));
                queries.add(sqlDataClearHelper.deleteTable(qf, ExemplarDb.KAT1_5.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KAT1_7.KAT1_7));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.SYNCHRONIZATION_CNB.TABLE));
                if (dbUpdateDatabaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)) {
                    queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KAT1_3.TABLE));
                    queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KAT1_2.TABLE));
                } else {
                    queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_KEYWORD_RELATION.TABLE));
                    queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_KEYWORD.TABLE));
                }
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KAT1_1.TABLE));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, RecordDb.KAT1_4.TABLE, RecordDb.KAT1_4.ID_ZAZ, List.of(0L)));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.UZIV_KONTAKTY.TABLE, UserDb.UZIV_KONTAKTY.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserPreferenceDb.USER_PREFS.USER_PREFS, UserPreferenceDb.USER_PREFS.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, VerbisDb.UZIV_DEFS.TABLE, VerbisDb.UZIV_DEFS.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.UZIV_VAZBY.UZIV_VAZBY, UserDb.UZIV_VAZBY.FK_UZIV_MEMBER_OR_CHILD, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserServicePropertyDb.USER_SERVICE_PROPERTY.TABLE, UserServicePropertyDb.USER_SERVICE_PROPERTY.USER_ID, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.VAZ_CTEN_PUJC.TABLE, UserDb.VAZ_CTEN_PUJC.FK_UZIV_CTEN, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.VAZ_UZIV_PUJC.TABLE, UserDb.VAZ_UZIV_PUJC.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_EXEMP.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, TransactionDb.PLATBY.PLATBY));
                queries.add(sqlDataClearHelper.deleteTable(qf, PaymentDb.PLACENI_GOPAY.PLACENI_GOPAY));
                queries.add(sqlDataClearHelper.deleteTable(qf, PaymentDb.PLACENI_CSOBGW.PLACENI_CSOBGW));
                queries.add(sqlDataClearHelper.deleteTable(qf, PaymentDb.PLACENI_GPWEBPAY.PLACENI_GPWEBPAY));
                queries.add(sqlDataClearHelper.deleteTable(qf, PaymentDb.PLACENI.TABLE));

                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_EMAIL.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_EMAIL_ADDRESS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_INTERNAL.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_POST.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_SMS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.THREAD_PARTICIPANT.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE.TABLE));

                queries.add(sqlDataClearHelper.updateTable(qf, LoggingDb.EVENT.TABLE, LoggingDb.EVENT.INITIATOR_USER_ID, suUserProvider.get().getId().toString(), LoggingDb.EVENT.INITIATOR_USER_ID, protectedUserIds));
                queries.add(sqlDataClearHelper.updateTable(qf, RecordDb.KATAUT_4.TABLE, RecordDb.KATAUT_4.FK_UZIV, suUserProvider.get().getId().toString(), RecordDb.KATAUT_4.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.updateTable(qf, RecordDb.KATAUT_4.TABLE, RecordDb.KATAUT_4.FK_UZIV_KORP, suUserProvider.get().getId().toString(), RecordDb.KATAUT_4.FK_UZIV_KORP, protectedUserIds));

                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.CTENARI.TABLE, UserDb.CTENARI.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_VYPUC.TABLE)); // TOTO je tu 2x
                queries.add(sqlDataClearHelper.deleteTable(qf, UserDb.DODAVATELE.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, BoxDb.BOX_STATION.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, UserDb.KNIHOVNY.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, UserDb.INSTITUCE.TABLE));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.KNIHOVNICI.TABLE, UserDb.KNIHOVNICI.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.OSOBY.TABLE, UserDb.OSOBY.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordCollectionDb.USER_RECORD_COLLECTION.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordCollectionDb.RECORD_COLLECTION_ITEM.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordCollectionDb.RECORD_COLLECTION.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_HOLDING_SOURCE.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_HOLDING.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.AUT_VAZBY.AUT_VAZBY));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KATAUT_1.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KATAUT_3.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KATAUT_2.TABLE));

                queries.add(sqlDataClearHelper.updateTableSetNullWhereNotNull(qf, RecordDb.KATAUT_4.TABLE, RecordDb.KATAUT_4.FK_AUT, RecordDb.KATAUT_4.FK_AUT));

                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.KATAUT_4.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_KEY.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.RECORD_FIELD.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, RecordDb.OBALKYKNIH.TABLE));

                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, UserDb.UZIVATELE.TABLE, UserDb.UZIVATELE.ID_UZIV, protectedUserIds));

                queries.add(RecordHardDeleter.scheduleReindexAllRecordsQueryExcept(qf, protectedRecordIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, RecordDb.RECORD.TABLE, RecordDb.RECORD.ID, protectedRecordIds));

                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, FileDb.FULLTEXT_SOUBORY.TABLE, FileDb.FULLTEXT_SOUBORY.FK_FULLTEXT_SKUPINY, protectedFulltextGroupIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, FileDb.FULLTEXT_SKUPINY.TABLE, FileDb.FULLTEXT_SKUPINY.ID_FULLTEXT_SKUPINY, protectedFulltextGroupIds));
                queries.add(sqlDataClearHelper.deleteTableWithExcludedIds(qf, VerbisDb.UZIV_TABCOLS.TABLE, VerbisDb.UZIV_TABCOLS.FK_UZIV, protectedUserIds));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING_EMAIL_ADDRESS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE_SENDING.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.MESSAGE.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, MessageDb.THREAD_PARTICIPANT.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, VerbisDb.DEF_TABSORT.TABLE));

                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_AUTOMAT.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_AKCE_PRAVA.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_AKTUALIZACE.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_ERRORS.TABLE));
                queries.add(sqlDataClearHelper.deleteTable(qf, GeneralDbConstants.LOG_TISK.TABLE));

                if (dbUpdateDatabaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)) {
                    FIREBIRD_ZAL_TABLES.forEach(
                            name -> queries.add(sqlDataClearHelper.deleteTable(qf, name))
                    );
                }

                queries.forEach(query ->
                        jdbc.update(query.getSql(), query.getParamMap())
                );
            });

        } catch (Exception e) {
            try {
                appserverConfigService.enableIndexing();
            } catch (Exception enableIndexingException) {
                log.error("Exception while enabling indexing in appserver", enableIndexingException);
            }
            throw e;
        }

        appserverConfigService.enableIndexing();
    }
}
