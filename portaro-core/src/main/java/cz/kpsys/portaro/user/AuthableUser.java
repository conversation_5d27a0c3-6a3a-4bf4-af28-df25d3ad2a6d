package cz.kpsys.portaro.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

public interface AuthableUser extends BasicUser {

    boolean isActive();

    @Nullable
    @NullableNotBlank
    String getUsername();

    default boolean hasUsername(@NonNull String username) {
        return username.equals(getUsername());
    }

    @JsonIgnore
    @Nullable
    @NullableNotBlank
    String getPasswordHash();

    @NonNull
    @NotEmpty
    List<Department> getReadableDepartments();

    @NonNull
    List<Department> getEditableDepartments();

    @NonNull
    Set<String> getRole();

    @NonNull
    Set<UserRole> getUserRoles();

    @Nullable
    UUID getRecordId();

    /**
     * Stream rolí po<PERSON>ado<PERSON>ho typu, které jsou vyhledány v daném departmentu a jemu podřízených.
     * Zatím vrací role přes všechny departmenty.
     *
     * TODO: implement logic for filtering reader role department
     */
    default <R extends UserRole> Stream<R> roleStreamOn(Class<R> roleClass, Department ctx) {
        return roleStream(roleClass);
    }

    default <R extends UserRole> Stream<R> roleStream(Class<R> roleClass) {
        return getUserRoles().stream()
                .filter(roleClass::isInstance)
                .map(roleClass::cast);
    }

    /**
     * TODO: implement logic for filtering reader role department
     */
    default boolean hasRoleOn(Class<? extends UserRole> roleClass, Department ctx) {
        return roleStreamOn(roleClass, ctx).findAny().isPresent();
    }

    default boolean hasRole(Class<? extends UserRole> roleClass) {
        return getUserRoles().stream().anyMatch(roleClass::isInstance);
    }

}
