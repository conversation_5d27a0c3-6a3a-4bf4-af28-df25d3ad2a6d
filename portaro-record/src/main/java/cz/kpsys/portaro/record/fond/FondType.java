package cz.kpsys.portaro.record.fond;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.DefaultProvider;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
@Getter
public enum FondType implements Identified<String> {

    STANDARD("standard"),
    AUTHORITY("authority"),
    DOCUMENT("document"),
    ACQUISITION("acquisition"),
    ELOAN("eloan"),
    ILL("ill"),
    SUPPLIER("supplier"),
    USER_PERSON("user_person"),
    DAY("day"),
    THREAD("thread"),
    DEPARTMENT("department");

    public static final Codebook<FondType, String> CODEBOOK = new StaticCodebook<>(values());
    public static final Provider<@NonNull FondType> DEFAULT_PROVIDER = DefaultProvider.byId(FondType.CODEBOOK, FondType.STANDARD.getId());

    @NonNull String id;

    public boolean matches(@NonNull Fond fond) {
        return fond.getFondType() == this;
    }

}
