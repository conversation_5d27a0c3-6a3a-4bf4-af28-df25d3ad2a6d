package cz.kpsys.portaro.erp.projectfinancialoverview.web;

import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.projectfinancialoverview.ProjectFinancialOverviewLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.UUID;

@RequestMapping("/api/project-financial-overview")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ProjectFinancialOverviewApiController {

    @NonNull ProjectFinancialOverviewLoader projectFinancialOverviewLoader;

    @RequestMapping
    public ProjectFinancialOverviewResponse get(@RequestParam("project") UUID project, @CurrentDepartment Department ctx) {
        return projectFinancialOverviewLoader.load(project, ctx);
    }
}
