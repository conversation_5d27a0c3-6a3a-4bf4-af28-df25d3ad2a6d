package cz.kpsys.portaro.erp;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.ProviderByIdProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.homepage.SutorHomepagePageDataProvider;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.erp.projectfinancialoverview.web.ProjectFinancialOverviewApiController;
import cz.kpsys.portaro.erp.projectfinancialoverview.ProjectFinancialOverviewLoader;
import cz.kpsys.portaro.erp.reportoptimisation.ReportOptimisationDayOverviewLoader;
import cz.kpsys.portaro.erp.reportoptimisation.ReportOptimisationOverallOverviewLoader;
import cz.kpsys.portaro.erp.reportoptimisation.ReportsOptimisationSumsLoader;
import cz.kpsys.portaro.erp.reportoptimisation.web.ReportOptimisationApiController;
import cz.kpsys.portaro.erp.workattendance.*;
import cz.kpsys.portaro.erp.workattendance.web.WorkAttendanceApiController;
import cz.kpsys.portaro.erp.workattendance.web.WorkAttendanceToResponseMapper;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ErpConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Integer> userIdSearchLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> nonDetailedRecordSearchSqlLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader;
    @NonNull WorkAttendanceReportLoader workAttendanceReportLoader;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Map<String, ContextualProvider<Department, ?>> pageDataProviders;
    @NonNull Provider<@NonNull ZoneId> defaultTimeZoneProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull Provider<@NonNull Fond> dayFondProvider;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull RecordDayIdLoader recordDayIdLoader;


    @Bean
    public WorkAttendanceApiController workAttendanceApiController() {
        return new WorkAttendanceApiController(
                recordLoader,
                activitySearchLoader(),
                defaultTimeZoneProvider,
                workAttendanceReportLoader,
                workAttendanceToResponseMapper(),
                recordDayIdLoader,
                idsToRecordsConverter,
                rangedJobFilesByUserRecordIdLoader(),
                rangedWorkCommitmentByUserRecordIdLoader()
        );
    }

    @Bean
    public DayApiController dayApiController() {
        return new DayApiController(
                recordDayHelper()
        );
    }

    @Bean
    public RecordDayHelper recordDayHelper() {
        return new RecordDayHelper(
                recordEditationFactory,
                recordEditationHelper,
                recordDayIdLoader,
                idsToRecordsConverter,
                dayFondProvider,
                recordEntryFieldTypeIdResolver,
                defaultTimeZoneProvider
        );
    }

    @Bean
    public ActivitySearchLoader activitySearchLoader() {
        return new ActivitySearchLoader(
                defaultTimeZoneProvider,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                costReportParentFondProvider(),
                workCostReportFondProvider(),
                absenceCostReportFondProvider(),
                operatedMachineCostReportFondProvider(),
                overheadCostReportFondProvider(),
                machineCostReportFondProvider()
        );
    }

    @Bean
    public RangedJobFilesByUserRecordIdLoader rangedJobFilesByUserRecordIdLoader() {
        return new RangedJobFilesByUserRecordIdLoader(jobFileByUserIdSearchLoader(), defaultTimeZoneProvider);
    }

    @Bean
    public RangedWorkCommitmentByUserRecordIdLoader rangedWorkCommitmentByUserRecordIdLoader() {
        return new RangedWorkCommitmentByUserRecordIdLoader(salaryByUserIdSearchLoader(), defaultTimeZoneProvider);
    }

    @Bean
    public WorkAttendanceToResponseMapper workAttendanceToResponseMapper() {
        return new WorkAttendanceToResponseMapper();
    }

    @Bean
    public Provider<@NonNull Fond> costReportParentFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 27).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> workCostReportFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 21).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> absenceCostReportFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 26).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> machineCostReportFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 22).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> operatedMachineCostReportFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 20).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> overheadCostReportFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 37).throwingWhenNull().cached();
    }

    @Bean
    public LinkedRecordSearchLoader jobFileByUserIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondJobContract.PersonLink.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                jobFileFondProvider()
        );
    }

    @Bean
    public LinkedRecordSearchLoader salaryByUserIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondJobContract.PersonLink.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                salaryFondProvider()
        );
    }

    @Bean
    public Provider<@NonNull Fond> salaryFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 8).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> jobFileFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 88).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> workPositionFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 70).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> professionWorkCatalogFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 16).throwingWhenNull().cached();
    }

    @Bean
    public Provider<@NonNull Fond> personWorkCatalogLinkFondProvider() {
        // TODO: create named fond (like day fond)
        return ProviderByIdProvider.ofStaticId(fondLoader, 87).throwingWhenNull().cached();
    }

    @Bean
    public LinkedRecordSearchLoader workCatalogLinkByUserIdSearchLoader() {
        return new LinkedRecordSearchLoader(
                RecordSutinSpecificFields.FondWorkCatalog.Person.Main.TYPE_ID,
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                personWorkCatalogLinkFondProvider()
        );
    }

    // Report optimisation
    @Bean
    public ReportOptimisationDayOverviewLoader reportOptimisationDayOverviewLoader() {
        return new ReportOptimisationDayOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                workCostReportFondProvider(),
                defaultTimeZoneProvider
        );
    }

    @Bean
    public ReportOptimisationOverallOverviewLoader reportOptimisationOverallOverviewLoader() {
        return new ReportOptimisationOverallOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                workCostReportFondProvider()
        );
    }

    @Bean
    public ReportsOptimisationSumsLoader reportsOptimisationSumsLoader() {
        return new ReportsOptimisationSumsLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                workCostReportFondProvider()
        );
    }

    @Bean
    public ReportOptimisationApiController reportOptimisationApiController() {
        return new ReportOptimisationApiController(
                reportOptimisationDayOverviewLoader(),
                reportOptimisationOverallOverviewLoader(),
                reportsOptimisationSumsLoader()
        );
    }

    // Project financial overview
    @Bean
    public ProjectFinancialOverviewLoader projectFinancialOverviewLoader() {
        return new ProjectFinancialOverviewLoader(
                contextHierarchyLoader,
                detailedRecordSearchSqlLoader,
                workCostReportFondProvider()
        );
    }

    @Bean
    public ProjectFinancialOverviewApiController projectFinancialOverviewApiController() {
        return new ProjectFinancialOverviewApiController(
                projectFinancialOverviewLoader()
        );
    }

    // Register page data providers
    @Bean
    public ContextualProvider<Department, ?> sutorHomepagePageDataProvider() {
        return new SutorHomepagePageDataProvider(
                settingLoader.getContextToValueMap(CoreSettingKeys.SERVER_URL),
                userIdSearchLoader,
                contextHierarchyLoader,
                nonDetailedRecordSearchSqlLoader,
                fondLoader,
                departmentAccessor
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        pageDataProviders.put("sutor-homepage", sutorHomepagePageDataProvider());
    }

}
