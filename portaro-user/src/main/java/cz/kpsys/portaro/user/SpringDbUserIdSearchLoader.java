package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.barcode.BarCodeValidator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.SelectedColumnRowMapper;
import cz.kpsys.portaro.databasestructure.UserServicePropertyDb.USER_SERVICE_PROPERTY;
import cz.kpsys.portaro.search.AbstractSingleColumnSpringDbSearchLoader;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.sorting.Sorting;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.Query;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.ContactType;
import cz.kpsys.portaro.user.prop.UserServicePropertySearchProperty;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.UserDb.*;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;
import static cz.kpsys.portaro.sql.generator.SelectQueryUtils.existsQuery;
import static cz.kpsys.portaro.sql.generator.SelectQueryUtils.existsQueryWhere;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbUserIdSearchLoader extends AbstractSingleColumnSpringDbSearchLoader<MapBackedParams, Integer, RangePaging> {

    @NonNull Provider<BarCodeValidator> userBarCodeValidatorProvider;

    public SpringDbUserIdSearchLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory, @NonNull Provider<BarCodeValidator> userBarCodeValidatorProvider) {
        super(jdbcTemplate, queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, new SelectedColumnRowMapper<>(Integer.class, UZIVATELE.ID_UZIV));
        this.userBarCodeValidatorProvider = userBarCodeValidatorProvider;
    }

    @Override
    protected void select(@NonNull SelectQuery sq, @NonNull MapBackedParams mapBackedParams, @Nullable SortingItem customSorting) {
        sq.selectDistinct(
                TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV),
                TC(UZIVATELE.TABLE, UZIVATELE.TRIDJMENO) // when we're sorting by this field, we must include it also in select expression
        );
    }

    @Override
    protected boolean fillQuery(boolean count, @NonNull SelectQuery sq, @NonNull MapBackedParams p, @Nullable SortingItem customSorting) {
        boolean joinWithReaders = false;
        boolean joinWithPersons = false;

        sq.from(UZIVATELE.TABLE);

        if (p.has(CoreSearchParams.Q)) {
            Brackets brackets = sq.where().and().brackets();
            String q = p.get(CoreSearchParams.Q);

            if (q.length() <= BasicUser.PRINTABLE_NAME_FLAT_MAX_LENGTH) {
                brackets.or().like(TC(UZIVATELE.TABLE, UZIVATELE.FLATJMENO), StringUtil.flatString(q.toUpperCase()), false, true, BasicUser.PRINTABLE_NAME_FLAT_MAX_LENGTH);

                if (q.length() <= (BasicUser.PRINTABLE_NAME_FLAT_MAX_LENGTH - 2)) {
                    // i slovo uprostred (= to, co ma pred sebou mezeru)
                    String qWithSpacePrefix = " " + StringUtil.flatString(q.toUpperCase());
                    brackets.or().like(TC(UZIVATELE.TABLE, UZIVATELE.FLATJMENO), qWithSpacePrefix, true, true, BasicUser.PRINTABLE_NAME_FLAT_MAX_LENGTH, false);
                }
            }

            if (q.length() <= User.USERNAME_MAX_LENGTH) {
                brackets.or().like(lower(TC(UZIVATELE.TABLE, UZIVATELE.USERNAME)), q.toLowerCase(), false, true, User.USERNAME_MAX_LENGTH);
            }

            if (userBarCodeValidatorProvider.get().isValid(StringUtil.diacriticsToDigits(q))) {
                String bc = StringUtil.diacriticsToDigits(q);

                Query existsSubquery = existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, CTENARI.TABLE, CTENARI.FK_UZIV, where -> {
                    where.and().in(lower(TC(CTENARI.TABLE, CTENARI.BAR_COD)), userBarCodeValidatorProvider.get().streamAllValidVariants(bc).map(String::toLowerCase).toList());
                    if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                        where.and().eq(TC(CTENARI.TABLE, CTENARI.JE_POVOL), true);
                    }
                });
                brackets.or().exists(existsSubquery);
            }

            if (q.length() <= ReaderRole.RFID_MAX_LENGTH) {
                Query existsSubquery = existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, CTENARI.TABLE, CTENARI.FK_UZIV, where -> {
                    where.and().in(TC(CTENARI.TABLE, CTENARI.RFID_UID), q.toUpperCase());
                    if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                        where.and().eq(TC(CTENARI.TABLE, CTENARI.JE_POVOL), true);
                    }
                });
                brackets.or().exists(existsSubquery);
            }
        }

        if (p.has(DEPARTMENT)) {
            if (!p.hasLength(DEPARTMENT)) {
                return false;
            }
            Query vazCtenPujc = existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, VAZ_CTEN_PUJC.TABLE, VAZ_CTEN_PUJC.FK_UZIV_CTEN, where ->
                    where.and().in(TC(VAZ_CTEN_PUJC.TABLE, VAZ_CTEN_PUJC.FK_PUJC), ListUtil.getListOfIds(p.get(DEPARTMENT))));
            Query vazUzivPujc = existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, VAZ_UZIV_PUJC.TABLE, VAZ_UZIV_PUJC.FK_UZIV, where ->
                    where.and().in(TC(VAZ_UZIV_PUJC.TABLE, VAZ_UZIV_PUJC.FK_PUJC), ListUtil.getListOfIds(p.get(DEPARTMENT))));
            sq.where()
                    .and()
                    .brackets()
                    .exists(vazCtenPujc)
                    .or()
                    .exists(vazUzivPujc);
        }

        if (p.has(UserConstants.SearchParams.READER_CATEGORY)) {
            if (!p.hasLength(UserConstants.SearchParams.READER_CATEGORY)) {
                return false;
            }
            joinWithReaders = true;
            sq.where().and().in(TC(CTENARI.TABLE, CTENARI.FK_CTENKAT), ListUtil.getListOfIds(p.get(UserConstants.SearchParams.READER_CATEGORY)));
        }

        if (p.has(UserConstants.SearchParams.CARD_NUMBER)) {
            if (isOverflow(p.get(UserConstants.SearchParams.CARD_NUMBER), ReaderRole.PROPERTY_CARD_NUMBER_MAX_LENGTH)) {
                return false;
            }
            joinWithReaders = true;
            sq.where().and().eq(lower(TC(CTENARI.TABLE, CTENARI.CIS_LEG)), p.get(UserConstants.SearchParams.CARD_NUMBER).toLowerCase());
        }

        if (p.has(UserConstants.SearchParams.BAR_CODE)) {
            String bc = StringUtil.diacriticsToDigits(p.get(UserConstants.SearchParams.BAR_CODE));
            userBarCodeValidatorProvider.get().throwIfInvalid(bc);
            joinWithReaders = true;
            sq.where().and().in(lower(TC(CTENARI.TABLE, CTENARI.BAR_COD)), userBarCodeValidatorProvider.get().streamAllValidVariants(bc).map(String::toLowerCase).toList());
        }

        if (p.has(UserConstants.SearchParams.EMAIL)) {
            List<String> emails = ListUtil.filter(p.get(UserConstants.SearchParams.EMAIL), s -> !isOverflow(s, Contact.PROPERTY_VALUE_MAX_LENGTH));
            if (emails.isEmpty()) {
                return false;
            }
            sq.joinOrExists(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, UZIV_KONTAKTY.TABLE, UZIV_KONTAKTY.FK_UZIV, "email", (where, alias) -> {
                                Brackets emailsBrackets = where.and().brackets();
                                for (String email : emails) {
                                    emailsBrackets.or()
                                            .eq(TC(alias, UZIV_KONTAKTY.TYP), ContactType.EMAIL.getId())
                                            .and()
                                            .eq(lower(TC(alias, UZIV_KONTAKTY.HODNOTA)), email.toLowerCase());
                                }
                            }
            );
        }

        if (p.has(UserConstants.SearchParams.RECORD_ID)) {
            if (!p.hasLength(UserConstants.SearchParams.RECORD_ID)) {
                return false;
            }
            sq.where().and().in(TC(UZIVATELE.TABLE, UZIVATELE.RECORD_ID), p.get(UserConstants.SearchParams.RECORD_ID));
        }

        if (p.has(UserConstants.SearchParams.USERNAME)) {
            if (isOverflow(p.get(UserConstants.SearchParams.USERNAME), User.USERNAME_MAX_LENGTH)) {
                return false;
            }
            sq.where().and().eq(TC(UZIVATELE.TABLE, UZIVATELE.USERNAME), p.get(UserConstants.SearchParams.USERNAME));
        }

        if (p.has(UserConstants.SearchParams.USERNAME_IGNORE_CASE)) {
            if (isOverflow(p.get(UserConstants.SearchParams.USERNAME_IGNORE_CASE), User.USERNAME_MAX_LENGTH)) {
                return false;
            }
            sq.where().and().eq(lower(TC(UZIVATELE.TABLE, UZIVATELE.USERNAME)), p.get(UserConstants.SearchParams.USERNAME_IGNORE_CASE).toLowerCase());
        }

        if (p.has(UserConstants.SearchParams.NATIVE_NAME)) {
            if (!p.hasLength(UserConstants.SearchParams.NATIVE_NAME)) {
                return false;
            }
            sq.where().and().in(TC(UZIVATELE.TABLE, UZIVATELE.ZOBR_JMENO), p.get(UserConstants.SearchParams.NATIVE_NAME));
        }

        if (p.has(UserConstants.SearchParams.SYNC_ID_PREFIX)) {
            if (isOverflow(p.get(UserConstants.SearchParams.SYNC_ID_PREFIX), User.SYNC_ID_LENGTH)) {
                return false;
            }
            sq.where().and().startsWith(TC(UZIVATELE.TABLE, UZIVATELE.SYNC_ID), p.get(UserConstants.SearchParams.SYNC_ID_PREFIX));
        }

        if (p.has(UserConstants.SearchParams.SYNC_ID)) {
            if (!p.hasLength(UserConstants.SearchParams.SYNC_ID)) {
                return false;
            }
            sq.where().and().in(TC(UZIVATELE.TABLE, UZIVATELE.SYNC_ID), p.get(UserConstants.SearchParams.SYNC_ID));
        }

        if (p.has(UserConstants.SearchParams.LAST_NAME)) {
            if (isOverflow(p.get(UserConstants.SearchParams.LAST_NAME), Person.LAST_NAME_MAX_LENGTH)) {
                return false;
            }
            joinWithPersons = true;
            sq.where().and().eq(trim(lower(TC(OSOBY.TABLE, OSOBY.PRIJMENI))), p.get(UserConstants.SearchParams.LAST_NAME).toLowerCase());
        }

        if (p.has(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID)) {
            List<String> caseInsensitiveNetId = ListUtil.filter(p.get(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID), s -> !isOverflow(s, Person.NET_ID_MAX_LENGTH));
            if (caseInsensitiveNetId.isEmpty()) {
                return false;
            }
            joinWithPersons = true;
            sq.where().and().in(lower(TC(OSOBY.TABLE, OSOBY.NET_ID)), ListUtil.convert(p.get(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID), String::toLowerCase));
        }

        if (p.has(UserConstants.SearchParams.OPENID)) {
            if (isOverflow(p.get(UserConstants.SearchParams.OPENID), Person.OPENID_MAX_LENGTH)) {
                return false;
            }
            joinWithPersons = true;
            sq.where().and().eq(TC(OSOBY.TABLE, OSOBY.OPENID), p.get(UserConstants.SearchParams.OPENID));
        }

        if (p.has(UserConstants.SearchParams.BAKALARI)) {
            if (!p.hasLength(UserConstants.SearchParams.BAKALARI)) {
                return false;
            }
            joinWithPersons = true;
            sq.where().and().in(TC(OSOBY.TABLE, OSOBY.BAKALARI), p.get(UserConstants.SearchParams.BAKALARI));
        }

        if (p.has(UserConstants.SearchParams.NOT_NULL_SOL_ID) & p.get(UserConstants.SearchParams.NOT_NULL_SOL_ID)) {
            joinWithPersons = true;
            sq.where().and().isNotNull(TC(OSOBY.TABLE, OSOBY.SOL_ID));
        }

        if (!p.get(UserConstants.SearchParams.INCLUDE_ANONYMIZED)) {
            sq.where().and().isNull(TC(UZIVATELE.TABLE, UZIVATELE.ANONYMIZATION_EVENT_ID));
        }

        if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(TC(UZIVATELE.TABLE, UZIVATELE.DELETION_EVENT_ID));
        }

        if (p.has(UserConstants.SearchParams.EDIT_LEVEL)) {
            sq.joinOrExists(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, KNIHOVNICI.TABLE, KNIHOVNICI.FK_UZIV, "edit_level", (where, alias) -> {
                where.and().eq(TC(alias, KNIHOVNICI.UROVEN), p.get(UserConstants.SearchParams.EDIT_LEVEL));
                if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                    where.and().eq(TC(alias, KNIHOVNICI.JE_POVOL), true);
                }
            });
        }

        if (p.has(UserConstants.SearchParams.SIGLA)) {
            List<String> siglas = ListUtil.filter(p.get(UserConstants.SearchParams.SIGLA), s -> !isOverflow(s, Library.SIGLA_MAX_LENGTH));
            if (siglas.isEmpty()) {
                return false;
            }
            sq.joinOrExists(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, KNIHOVNY.TABLE, KNIHOVNY.FK_UZIV, "library_sigla", (where, alias) -> {
                where.and().in(TC(alias, KNIHOVNY.SIGLA), siglas);
            });
        }

        if (p.has(UserConstants.SearchParams.LINKED_DEPARTMENT)) {
            sq.joinOrExists(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, KNIHOVNY.TABLE, KNIHOVNY.FK_UZIV, "library_linkeddept", (where, alias) -> {
                where.and().in(TC(alias, KNIHOVNY.FK_PUJC), p.get(UserConstants.SearchParams.LINKED_DEPARTMENT).getId());
            });
        }

        if (p.has(UserConstants.SearchParams.USER_SERVICE_PROP)) {
            sq.joinOrExists(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, USER_SERVICE_PROPERTY.TABLE, USER_SERVICE_PROPERTY.USER_ID, "user_service_property", (where, alias) -> {
                Brackets userServicePropertyDisj = sq.where().and().brackets();
                for (UserServicePropertySearchProperty userServicePropertyCommand : p.get(UserConstants.SearchParams.USER_SERVICE_PROP)) {
                    Brackets singleServiceProperty = userServicePropertyDisj.or().eq(TC(alias, USER_SERVICE_PROPERTY.SERVICE), userServicePropertyCommand.service());
                    if (userServicePropertyCommand.name() != null) {
                        singleServiceProperty.and().eq(TC(alias, USER_SERVICE_PROPERTY.PROP_NAME), userServicePropertyCommand.name());
                    }
                    if (userServicePropertyCommand.value() != null) {
                        singleServiceProperty.and().eq(TC(alias, USER_SERVICE_PROPERTY.PROP_VALUE), userServicePropertyCommand.value());
                    }
                    if (userServicePropertyCommand.validityEndDate() != null) {
                        singleServiceProperty.and().eq(TC(alias, USER_SERVICE_PROPERTY.VALIDITY_END_DATE), userServicePropertyCommand.validityEndDate());
                    }
                }
            });
        }

        if (p.has(UserConstants.SearchParams.USER_TYPE)) {
            if (!p.hasLength(UserConstants.SearchParams.USER_TYPE)) {
                return false;
            }
            Brackets userTypesDisj = sq.where().and().brackets();
            for (UserType userType : p.get(UserConstants.SearchParams.USER_TYPE)) {
                switch (userType) {
                    case LIBRARIAN -> userTypesDisj.or().exists(existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, KNIHOVNICI.TABLE, KNIHOVNICI.FK_UZIV, where -> {
                        if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                            where.and().eq(TC(KNIHOVNICI.TABLE, KNIHOVNICI.JE_POVOL), true);
                        }
                    }));
                    case PERSON_READER -> userTypesDisj.or().brackets()
                            .exists(existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, CTENARI.TABLE, CTENARI.FK_UZIV, where -> {
                                if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                                    where.and().eq(TC(CTENARI.TABLE, CTENARI.JE_POVOL), true);
                                }
                            }))
                            .and()
                            .exists(existsQuery(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, OSOBY.TABLE, OSOBY.FK_UZIV));
                    case LIBRARY -> userTypesDisj.or().exists(existsQuery(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, KNIHOVNY.TABLE, KNIHOVNY.FK_UZIV));
                    case COMPANY -> userTypesDisj.or().exists(existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, INSTITUCE.TABLE, INSTITUCE.FK_UZIV, where ->
                            where.and().eq(TC(INSTITUCE.TABLE, INSTITUCE.TYP_INSTITUCE), InstitutionType.COMPANY.getId())));
                    case FAMILY -> userTypesDisj.or().exists(existsQueryWhere(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, INSTITUCE.TABLE, INSTITUCE.FK_UZIV, where ->
                            where.and().eq(TC(INSTITUCE.TABLE, INSTITUCE.TYP_INSTITUCE), InstitutionType.FAMILY.getId())));
                    case SOFTWARE -> userTypesDisj.or().exists(existsQuery(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, SOFTWARE.TABLE, SOFTWARE.FK_UZIV));
                    case SUPPLIER -> userTypesDisj.or().exists(existsQuery(queryFactory, UZIVATELE.TABLE, UZIVATELE.ID_UZIV, DODAVATELE.TABLE, DODAVATELE.FK_UZIV));
                    default -> throw new UnsupportedOperationException("User type " + userType + " is not supported in user search");
                }
            }
        }

        if (p.has(UserConstants.SearchParams.RFID_USER_ID)) {
            if (isOverflow(p.get(UserConstants.SearchParams.RFID_USER_ID), ReaderRole.RFID_MAX_LENGTH)) {
                return false;
            }
            joinWithReaders = true;
            sq.where().and().in(TC(CTENARI.TABLE, CTENARI.RFID_UID), p.get(UserConstants.SearchParams.RFID_USER_ID));
        }

        //joins
        if (joinWithReaders) {
            sq.joins().add(CTENARI.TABLE, COLSEQ(TC(CTENARI.TABLE, CTENARI.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)));
            if (!p.get(CoreSearchParams.INCLUDE_DELETED)) {
                sq.where().and().eq(TC(CTENARI.TABLE, CTENARI.JE_POVOL), true);
            }
        }

        if (joinWithPersons) {
            sq.joins().add(OSOBY.TABLE, COLSEQ(TC(OSOBY.TABLE, OSOBY.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)));
        }

        return true;
    }

    @Override
    protected Sorting mandatorySorting(@Nullable SortingItem customSorting, @NonNull MapBackedParams p) {
        return Sorting.ofAsc(TC(UZIVATELE.TABLE, UZIVATELE.TRIDJMENO), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV));
    }

    private boolean isOverflow(String value, int maxLength) {
        return value.length() > maxLength;
    }
}
