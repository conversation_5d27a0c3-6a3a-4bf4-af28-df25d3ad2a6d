package cz.kpsys.portaro.user;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.property.PropertyFactory;
import cz.kpsys.portaro.search.SearchParamsConstants;
import cz.kpsys.portaro.template.BasicTemplateDescriptor;
import cz.kpsys.portaro.template.TemplateDescriptor;
import cz.kpsys.portaro.user.prop.UserServicePropertySearchProperty;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.util.Assert;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class UserConstants {

    public static class Datatype {
        public static final ScalarDatatype READER_CATEGORY = scalar("CTEN_KAT");
        public static final ScalarDatatype USER_TYPE = scalar("USER_TYPE");
        public static final ScalarDatatype USER_SERVICE_PROP = scalar("USER_SERVICE_PROP");
    }

    public static class Template {
        public static final TemplateDescriptor TEMPLATE_USER_PASSWORD_RESET_MAIL = new BasicTemplateDescriptor("html", "person-password-reset-mail", "vtl");
        public static final TemplateDescriptor TEMPLATE_PERSON_REGISTRATION_AGREEMENT_PRINT = new BasicTemplateDescriptor("html", "person-registration-agreement-print", "vtl");
        public static final TemplateDescriptor TEMPLATE_USER_REGISTRATION_SUCCESS_MAIL = new BasicTemplateDescriptor("html", "person-registration-success-mail", "vtl");
        public static final TemplateDescriptor TEMPLATE_DISCOUNT_REQUEST = new BasicTemplateDescriptor("html", "discount-request", "vtl");
    }

    public static class SearchParams implements SearchParamsConstants {

        public static final JavatypedDatatypedProperty<List<UUID>> RECORD_ID = PropertyFactory.ofSearchProperty("boundRecord", Texts.ofNative("Bound record"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.UUID), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UUID.class)));

        public static final JavatypedDatatypedProperty<String> USERNAME = PropertyFactory.<String>ofSearchProperty("username", Texts.ofMessageCoded("ctenar.username"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> USERNAME_IGNORE_CASE = PropertyFactory.<String>ofSearchProperty("usernameIgnoreCase", Texts.ofMessageCoded("ctenar.username"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<List<String>> EMAIL = PropertyFactory.ofSearchProperty("email", Texts.ofNative("Email"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<String> LAST_NAME = PropertyFactory.<String>ofSearchProperty("lastName", Texts.ofMessageCoded("ctenar.lastName"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<List<String>> NATIVE_NAME = PropertyFactory.ofSearchProperty("nativeName", Texts.ofMessageCoded("Printable name"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<String> CARD_NUMBER = PropertyFactory.<String>ofSearchProperty("cardNumber", Texts.ofMessageCoded("ctenar.cardNumber"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> BAR_CODE = PropertyFactory.<String>ofSearchProperty("barCode", Texts.ofMessageCoded("ctenar.barCode"), CoreConstants.Datatype.BAR_CODE, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<List<String>> SIGLA = PropertyFactory.ofSearchProperty("sigla", Texts.ofMessageCoded("department.Sigla"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<List<String>> CASE_INSENSITIVE_NET_ID = PropertyFactory.ofSearchProperty("caseInsensitiveNetId", Texts.ofNative("NetId"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<List<String>> BAKALARI = PropertyFactory.ofSearchProperty("bakalari", Texts.ofNative("Bakalari"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<Boolean> NOT_NULL_SOL_ID = PropertyFactory.<Boolean>ofSearchProperty("notNullSolId", Texts.ofMessageCoded("user.NonZeroSolID"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class))
                .withDefault(Boolean.FALSE);

       public static final JavatypedDatatypedProperty<String> SYNC_ID_PREFIX = PropertyFactory.<String>ofSearchProperty("syncIdPrefix", Texts.ofNative("SynchonizationIdPrefix"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<List<String>> SYNC_ID = PropertyFactory.ofSearchProperty("syncId", Texts.ofNative("SynchonizationId"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<String> OPENID = PropertyFactory.<String>ofSearchProperty("openId", Texts.ofNative("OpenID"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> RFID_USER_ID = PropertyFactory.<String>ofSearchProperty("rfid", Texts.ofNative("RFID"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<List<ReaderCategory>> READER_CATEGORY = PropertyFactory.ofSearchProperty("readerCategory", Texts.ofMessageCoded("ctenar.readerCategory"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.READER_CATEGORY), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(ReaderCategory.class)));

        public static final JavatypedDatatypedProperty<List<UserType>> USER_TYPE = PropertyFactory.ofSearchProperty("userType", Texts.ofMessageCoded("user.Type"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.USER_TYPE), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UserType.class)));

        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_ANONYMIZED = PropertyFactory.<Boolean>ofSearchProperty("includeAnonymized", Texts.ofMessageCoded("user.IncludingAnonymized"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class))
                .withDefault(Boolean.FALSE);

        public static final JavatypedDatatypedProperty<Integer> EDIT_LEVEL = PropertyFactory.<Integer>ofSearchProperty("editLevel", Texts.ofMessageCoded("user.EditingLevel"), CoreConstants.Datatype.NUMBER, TypeDescriptor.valueOf(Integer.class))
                .checkNotnullModifiedValue(editLevel -> Assert.isTrue(editLevel >= 0, "Edit level cannot be negative number"));

        public static final JavatypedDatatypedProperty<List<BasicUser>> CREATOR = PropertyFactory.ofSearchProperty("creator", Texts.ofMessageCoded("user.Creator"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.USER), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(BasicUser.class)));

        public static final JavatypedDatatypedProperty<Department> LINKED_DEPARTMENT = PropertyFactory.ofSearchProperty("linkedDepartment", Texts.ofNative("Navázané oddělení"), CoreConstants.Datatype.DEPARTMENT, TypeDescriptor.valueOf(Department.class));

        public static final JavatypedDatatypedProperty<List<UserServicePropertySearchProperty>> USER_SERVICE_PROP = PropertyFactory.ofSearchProperty("userServiceProp", Texts.ofNative("Vlastnost služby uživatele"), Datatype.USER_SERVICE_PROP, TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(
                UserServicePropertySearchProperty.class)));
    }

}
