package cz.kpsys.portaro.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import java.util.UUID;

import static cz.kpsys.portaro.user.BasicUser.createNewUserId;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@FieldNameConstants
public class Institution extends User implements NativeNamedUser {

    public static boolean isFamily(BasicUser basicUser) {
        return basicUser instanceof Institution institution && InstitutionType.FAMILY.equals(institution.getInstitutionType());
    }

    @Deprecated
    final String kind = "INSTITUTION";

    @NonNull
    UserStringGenerator prettyUserNameGenerator;

    @Setter
    @NullableNotBlank
    String name;

    @Setter
    @NullableNotBlank
    String ico;

    @Setter
    @NullableNotBlank
    String dic;

    @Setter
    @NullableNotBlank
    String homepageUrl;

    @Setter
    @NotNull
    InstitutionType institutionType;

    public Institution(@NonNull UserStringGenerator prettyUserNameGenerator, Integer id, String name, InstitutionType institutionType) {
        super(id);
        this.prettyUserNameGenerator = prettyUserNameGenerator;
        this.name = name;
        this.institutionType = institutionType;
    }

    public static Institution createNew(@NonNull UserStringGenerator prettyUserNameGenerator, @NonNull InstitutionType institutionType) {
        return new Institution(prettyUserNameGenerator, createNewUserId(), null, institutionType);
    }

    public static Institution testingSupplierInstitution(int supplierCompanyUserId, UUID supplierCompanyAuthorityId) {
        Institution institution = new Institution(UserStringGenerator.testing(), supplierCompanyUserId, "Record %s".formatted(supplierCompanyAuthorityId), InstitutionType.COMPANY);
        institution.setRecordId(supplierCompanyAuthorityId);
        return (Institution) institution.addUserRole(SupplierRole.create(supplierCompanyUserId, supplierCompanyUserId));
    }

    @JsonIgnore
    @Override
    protected @NonNull String getPrettyName() {
        return prettyUserNameGenerator.generate(this);
    }

    @NullableNotBlank
    @Override
    public String getNativeName() {
        return name;
    }
}
