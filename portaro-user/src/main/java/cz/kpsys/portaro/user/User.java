package cz.kpsys.portaro.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.RIdentified;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.UserAddress;
import cz.kpsys.portaro.user.prop.UserServiceProperty;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@FieldNameConstants
public abstract class User extends BasicIdentified<Integer> implements AuthableUser, RIdentified<UUID> {

    public static final int SYNC_ID_LENGTH = 50;
    public static final int USERNAME_MAX_LENGTH = 50;
    public static final int PASSWORD_MAX_LENGTH = 80;
    public static final int PASSWORD_MIN_LENGTH = 4;

    public static final String EXAMPLE_LIBRARY_ID = "8614";
    public static final String SCHEMA_EXAMPLE_PERSON_ID = "5641";

    final String kind = "USER";

    @Setter
    @Nullable
    UUID rid;

    @NonNull
    final Set<UserRole> userRoles = new HashSet<>();

    @Setter
    @Nullable // can be null when user is not already saved in db
    UUID creationEventId;

    @Setter
    @Nullable
    UUID activationEventId;

    @JsonIgnore
    @Setter
    @Nullable
    UUID anonymizationEventId;

    @Setter
    @Nullable
    UUID deletionEventId;

    @JsonIgnore
    @Setter
    @NotNull
    List<@Valid Contact> emails = new ArrayList<>();

    @JsonIgnore
    @Setter
    @NotNull
    List<@Valid Contact> phoneNumbers = new ArrayList<>();

    @Setter
    @NotNull
    List<@Valid UserAddress> addresses = new ArrayList<>();

    @Setter
    @NotNull
    List<@Valid UserServiceProperty> userServiceProperties = new ArrayList<>();

    @Setter
    @NullableNotBlank
    String username;

    @Setter
    @NullableNotBlank
    String syncId;

    @Setter
    @Nullable
    UUID recordId;

    @Setter
    @NullableNotBlank
    @JsonIgnore
    String passwordHash;

    @Setter
    @NotEmpty
    List<Department> readableDepartments;

    @Setter
    @Nullable
    List<Department> editableDepartments;


    public User(@NonNull Integer id) {
        super(id);
    }

    @Override
    public boolean isEvided() {
        return BasicUser.isEvided(getId());
    }

    public boolean isActive() {
        return getActivationEventId() != null;
    }

    public boolean isDeleted() {
        return getDeletionEventId() != null;
    }

    @JsonIgnore
    @NonNull
    protected String getPrettyName() {
        return "User#%s".formatted(getId());
    }

    @Override
    public Text getText() {
        return Texts.ofMessageCodedOrNativeOrEmptyNative("users.%s.name".formatted(getId()), getPrettyName());
    }

    @Override
    @NonNull
    public Set<String> getRole() {
        return new RoleNamesResolver().resolveRoles(this);
    }

    public User addUserRole(@NonNull UserRole role) {
        synchronized (userRoles) {
            userRoles.remove(role); // to replace old, if current role is equal
            userRoles.add(role);
        }
        return this;
    }

    public void removeUserRole(@NonNull UserRole role) {
        userRoles.remove(role);
    }

    @Deprecated
    @Nullable
    public ReaderRole getReaderRole() {
        return roleStream(ReaderRole.class).findFirst().orElse(null);
    }

    @NonNull
    public List<ReaderRole> getReaderAccounts() {
        return roleStream(ReaderRole.class).toList();
    }

    @NonNull
    public List<EditorAccount> getEditorAccounts() {
        return roleStream(EditorAccount.class).toList();
    }

    @NonNull
    public List<SupplierRole> getSupplierAccounts() {
        return roleStream(SupplierRole.class).toList();
    }

    @NonNull
    public List<Contact> getContacts() {
        return Stream.concat(getEmails().stream(), getPhoneNumbers().stream()).toList();
    }

    /**
     * E-mail, který má uživatel nastavený přímo na sobě. <em>Pokud chcete získat e-mail na uživatele, je potřeba spíš
     * použít {@link cz.kpsys.portaro.user.contact.ContactManager#getEmail(BasicUser)}, který bere v úvahu i zástupce
     * a rodiny.</em>
     *
     * @return e-mail uživatele
     */
    @NullableNotBlank
    public String getEmail() {
        return emails.stream()
                .map(Contact::getValue)
                .findFirst()
                .orElse(null);
    }

    @Override
    public boolean equals(Object obj) {
        return BasicUserImpl.equals(this, obj);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + " " + getId() + " (" + StringUtil.joinSkippingBlanksAndNulls(", ", getUsername(), isActive() ? "ACTIVE" : "NOT ACTIVE", getRole()) + ")";
    }

}
