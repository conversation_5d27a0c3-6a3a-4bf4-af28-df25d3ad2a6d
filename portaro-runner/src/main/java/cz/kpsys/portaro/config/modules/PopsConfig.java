package cz.kpsys.portaro.config.modules;

import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.config.*;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.database.IntegerValueDatabaseLoader;
import cz.kpsys.portaro.exemplar.volume.Volume;
import cz.kpsys.portaro.export.ExporterResolver;
import cz.kpsys.portaro.export.XlsFileExporterByCsvExporter;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.file.LoadedIdentifiedFileLoaderDelegating;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.pops.*;
import cz.kpsys.portaro.pops.agreement.*;
import cz.kpsys.portaro.pops.export.CsvFileTenderDemandsExporter;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResolver;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.template.Templates;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.role.supplier.SupplierRole;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.security.PermissionResolver.*;
import static cz.kpsys.portaro.security.PermissionResult.*;
import static cz.kpsys.portaro.user.BasicUser.ROLE_ADMIN;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@Configuration
public class PopsConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ModelBeanBuilder modelBeanBuilder;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull SettingLoader settingLoader;
    @NonNull AllByIdsLoadable<Record, UUID> richRecordLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull AllByIdsLoadable<Volume, Integer> volumeLoader;
    @NonNull IdAndIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull Provider<@NonNull Currency> defaultCurrencyProvider;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull Saver<IdentifiedFile, IdentifiedFile> loadedIdentifiedFileSaver;
    @NonNull TemplateEngine templateEngine;
    @NonNull ExporterResolver exporterResolver;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull Provider<Integer> portaroUserIdProvider;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull SecurityManager securityManager;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ActionSaver actionSaver;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordItemsWithoutExportsConverter;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull EntityManager entityManager;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;


    @Bean
    public TenderApiController tenderApiController() {
        log.debug("Enabling POPS module");
        return new TenderApiController(tenderLoader(), tenderSaver(), tenderDeleter());
    }

    @Bean
    public OfferApiController offerApiController() {
        return new OfferApiController(
                offerLoader(),
                offerDeleter(),
                defaultCurrencyProvider,
                offerCreator(),
                securityManager
        );
    }

    @Bean
    public DemandApiController demandApiController() {
        return new DemandApiController(
                securityManager,
                demandLoaderSecurityHelper(),
                tenderLoader(),
                recordsToViewableRecordItemsWithoutExportsConverter
        );
    }

    @Bean
    public AgreementApiController agreementApiController() {
        return new AgreementApiController(
                tenderLoader(),
                fileAgreementCreator(),
                agreementProcessor(),
                signedAgreementUploader()
        );
    }

    @Bean
    public OfferingStateApiController offeringStateApiController() {
        return new OfferingStateApiController(tenderLoader(), offeringStateResolver());
    }

    @Bean
    public AgreementConfirmer agreementConfirmer() {
        return new SignedFileVerifyingAgreementConfirmer(
                new ITextPdfAgreementFileVerifier(),
                agreementSaver(),
                settingLoader.getOnRootProvider(SettingKeys.AGREEMENT_STYLE));
    }

    @Bean
    public FileAgreementCreator fileAgreementCreator() {
        Provider<Boolean> agreementFileSigningEnabled = () -> settingLoader.getOnRootProvider(SettingKeys.AGREEMENT_STYLE).get() == AgreementStyle.FILE_SIGNING;
        return new FileAgreementCreatorSavingProxy(
                new FileAgreementCreatorITextPdf(demandLoader(), agreementFileSigningEnabled, nonDetailedRichRecordLoader),
                agreementSaver(),
                portaroUserIdProvider,
                agreementFileSigningEnabled);
    }


    @Bean
    public Saver<Agreement, Agreement> agreementSaver() {
        return new AgreementSaver(
                new PostConvertingSaver<>(
                        new PreConvertingSaver<>(
                                new AgreementToEntityConverter(),
                                new FlushingJpaSaver<>(new SimpleJpaRepository<>(AgreementEntity.class, entityManager))
                        ),
                        new AgreementFromEntityConverter(loadedIdentifiedFileLoader())
                ),
                loadedIdentifiedFileSaver
        );
    }

    @Bean
    public AgreementCreator buttonAgreementCreator() {
        return new ButtonAgreementCreator(agreementSaver(), defaultTransactionTemplateFactory.get());
    }

    @Bean
    public AgreementProcessor agreementProcessor() {
        return new AgreementProcessor(buttonAgreementCreator(), agreementConfirmer(), defaultTransactionTemplateFactory.get(), agreementLoader());
    }

    @Bean
    public SignedAgreementUploader signedAgreementUploader() {
        return new SignedAgreementUploader(defaultTransactionTemplateFactory.get(), agreementConfirmer(), agreementSaver(), agreementLoader(), tenderLoader());
    }

    @Bean
    public OfferingStateResolver offeringStateResolver() {
        return new OfferingStateResolver(agreementLoader(), offerLoader(), settingLoader.getOnRootProvider(SettingKeys.AGREEMENT_STYLE));
    }

    @Bean
    public AgreementLoader agreementLoader() {
        return new SpringDbAgreementLoader(jdbcTemplate, queryFactory, loadedIdentifiedFileLoader());
    }

    @Bean
    public ByIdLoadable<LoadedIdentifiedFile, Long> loadedIdentifiedFileLoader() {
        return new LoadedIdentifiedFileLoaderDelegating(identifiedFileLoader, fileDataStreamer);
    }

    @Bean
    public Deleter<Agreement> agreementDeleter() {
        return new SoftDeleterBySaver<>(agreementSaver());
    }

    @Bean
    public TenderLoader tenderLoader() {
        Codebook<Tender, Integer> tenderCodebook = codebookLoaderBuilderFactory.create()
                .providedByJpa(TenderEntity.class, TenderEntity.Fields.createDate)
                .converted(new EntitiesToTendersConverter(basicUserLoader))
                .staticCached(Tender.class.getSimpleName())
                .build();
        return new TenderLoaderDelegating(tenderCodebook);
    }

    @Bean
    public Saver<Tender, Tender> tenderSaver() {
        return saverBuilderFactory.<Tender, Integer>saver()
                .intermediateConverting(new TenderToRowConverter())
                .idSetting(TenderEntity.class, new StringToIntegerConverter(), IdSettable::setId)
                .withClearedCacheName(TenderEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public Deleter<Tender> tenderDeleter() {
        return modelBeanBuilder.hibernateSoftDeleter(TenderEntity.class, new TenderToRowConverter())
                .withClearedCacheName(TenderEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public OfferLoader offerLoader() {
        return new SpringDbOfferLoader(
                jdbcTemplate,
                queryFactory,
                new EntitiesToOffersConverter(
                        nonDetailedRichRecordLoader,
                        basicUserLoader
                )
        );
    }

    @Bean
    public Saver<Offer, Offer> offerSaver() {
        return new ActionDecoratingSaver<>(
                new PostConvertingSaver<>(
                        new PreConvertingSaver<>(
                                new OfferToEntityConverter(),
                                new FlushingJpaSaver<>(new SimpleJpaRepository<>(OfferEntity.class, entityManager))
                        ),
                        new OfferFromEntityConverter(basicUserLoader, nonDetailedRichRecordLoader)
                )).doAfter(offer -> {
            Optional<Agreement> agreementToInvalidate = agreementLoader().getBySupplierAndTender(offer.getSupplierAuthority().getId(), offer.getTenderId());
            agreementToInvalidate.ifPresent(agreementDeconfirmer()::deconfirm);
        });
    }

    @Bean
    public OfferCreator offerCreator() {
        return new OfferCreator(
                offerSaver(),
                defaultTransactionTemplateFactory.get(),
                Provider.of(IntegerValueDatabaseLoader.ofSequenceValue(PopsDb.POPS_NABIDKY.SEQ_POPS_NABIDKY, notAutoCommittingJdbcTemplate, queryFactory)),
                securityManager,
                demandLoader(),
                offerLoader(),
                actionSaver,
                nonDetailedRichRecordLoader
        );
    }

    @Bean
    public Deleter<OfferDeletionCommand> offerDeleter() {
        return new OfferDeleter(
                new SoftDeleterBySaver<>(offerSaver()),
                actionSaver,
                demandLoader(),
                offerLoader(),
                securityManager,
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public AgreementDeconfirmer agreementDeconfirmer() {
        TransactionTemplate transactionTemplate = defaultTransactionTemplateFactory.get();
        return agreement -> transactionTemplate.executeWithoutResult(_ -> agreementDeleter().delete(agreement));
    }

    @Bean
    public DemandLoader demandLoader() {
        return new SpringDbDemandLoader(jdbcTemplate, queryFactory, offerLoader(), new EntitiesToDemandsConverter(richRecordLoader, volumeLoader, tenderLoader()));
    }

    @Bean
    public DemandLoaderSecurityHelper demandLoaderSecurityHelper() {
        return new DemandLoaderSecurityHelperImpl(securityManager, demandLoader());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerExports() {
        CsvFileTenderDemandsExporter tenderOffersCsvExporter = new CsvFileTenderDemandsExporter("tender-demands.csv", Templates.TEMPLATE_TENDER_OFFERS_CSV, templateEngine, demandLoader());
        exporterResolver.addStatic("CsvFileTenderDemands", tenderOffersCsvExporter);

        XlsFileExporterByCsvExporter<Tender> tenderOffersXlsExporter = new XlsFileExporterByCsvExporter<>("tender-demands.xls", tenderOffersCsvExporter);
        exporterResolver.addStatic("XlsFileTenderDemands", tenderOffersXlsExporter);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForIntegerId(Tender.class, tenderLoader());
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        PermissionResolver<Offer> currentEvidedAuthenticActiveCanRepresentOfferSupplier = and(
                permissionFactory.currentEvidedAuthenticActive(),
                (auth, _, offer) -> {
                    UUID userAuthority = auth.getActiveUser().getRecordId();
                    boolean userCanRepresentCompanyOfThisOffer = userAuthority != null && userAuthority.equals(offer.getSupplierAuthority().getId());
                    return ifCan(userCanRepresentCompanyOfThisOffer);
                }
        );

        //drivejsi nez opened (tzn. created) muze videt pouze administrator
        permissionRegistry.add(PopsSecurityActions.POPS_TENDER_SHOW, or(
                (auth, ctx, tender) -> tender.getPhase() >= Tender.PHASE_OPENED ? allow() : cannot(auth, Texts.ofNative("Cannot show not-opened tender")),
                permissionFactory.edit()
        ));

        //vytvaret muze zatim jen admin (aktualne jen v testech)
        permissionRegistry.add(PopsSecurityActions.POPS_TENDER_SAVE, and(
                adaptingSubject(Tender::getCreator, permissionFactory.currentIsSubjectUser()),
                permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_ADMIN)
        ));

        permissionRegistry.add(PopsSecurityActions.POPS_TENDER_DELETE, adaptingSubject(Tender::getCreator, permissionFactory.currentIsSubjectUser()));

        //admin a knihovnik vidi vsechny nabidky
        permissionRegistry.add(PopsSecurityActions.POPS_ALL_OFFERS_SHOW, permissionFactory.edit());

        //dodavatel vidi pouze sve nabidky
        permissionRegistry.add(PopsSecurityActions.POPS_SUPPLIER_OFFERS_SHOW, and(
                permissionFactory.currentEvidedAuthenticActiveIsSubjectUser(),
                permissionFactory.subjectUserIsEvidedActiveWithRole(SupplierRole.class)
        ));

        permissionRegistry.add(PopsSecurityActions.POPS_OFFER_SHOW, or(
                permissionFactory.edit(),
                currentEvidedAuthenticActiveCanRepresentOfferSupplier
        ));

        permissionRegistry.add(PopsSecurityActions.POPS_OFFER_SAVE, or(
                permissionFactory.currentEvidedAuthenticActiveWithAnyOfRoles(ROLE_ADMIN),
                currentEvidedAuthenticActiveCanRepresentOfferSupplier
        ));

        permissionRegistry.add(PopsSecurityActions.POPS_OFFER_DELETE, permissionRegistry.getLazy(PopsSecurityActions.POPS_OFFER_SAVE));
    }

}
