import type {LogService} from 'core/logging/log.service';

export type HttpVersion = 'http/0.9' | 'http/1.0' | 'http/1.1' | 'h2' | 'h2c' | 'h3';

const FALLBACK_HTTP_VERSION = 'http/1.1';

export class NetworkInfoService {
    public static serviceName = 'networkInfoService';

    /*@ngInject*/
    constructor(private logService: LogService) {
    }

    public httpVersion(): HttpVersion {
        try {
            const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
            return (navEntry?.nextHopProtocol ?? FALLBACK_HTTP_VERSION) as HttpVersion;
        } catch (e) {
            this.logService.warn('Could not detect HTTP version, falling back to HTTP/1.1', e);
            return FALLBACK_HTTP_VERSION;
        }
    }

    public isHttp1x(): boolean {
        const version = this.httpVersion();
        return version === 'http/0.9' || version === 'http/1.0' || version === 'http/1.1';
    }
}