import type {ToastMessage, ToastType} from 'shared/components/kp-toast-messages/types';
import type {Observable} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import {uuid} from 'shared/utils/custom-utils';
import {
    DEFAULT_TOAST_TIMEOUT_MS,
    DEFAULT_TOAST_TITLE,
    ERROR_TOAST_TIMEOUT_MS,
    MAX_TOASTS_COUNT
} from 'shared/components/kp-toast-messages/constants';

export class ToastMessageService {
    public static serviceName = 'toastMessageService';

    private readonly toasts$: BehaviorSubject<ToastMessage[]>;

    constructor() {
        this.toasts$ = new BehaviorSubject<ToastMessage[]>([]);
    }

    public showClickableNotification(title: string, message: string, clickHandler?: () => void): ToastMessage {
        return this.createToastMessage('info', message, title, null, clickHandler);
    }

    public showInfo(message: string, title?: string, timeout?: number): ToastMessage {
        return this.createToastMessage('info', message, title, timeout);
    }

    public showSuccess(message: string, title?: string, timeout?: number): ToastMessage {
        return this.createToastMessage('success', message, title, timeout);
    }

    public showError(message: string, title?: string, timeout?: number): ToastMessage {
        return this.createToastMessage('error', message, title, timeout ?? ERROR_TOAST_TIMEOUT_MS);
    }

    public showWait(message: string, title?: string, timeout?: number): ToastMessage {
        return this.createToastMessage('wait', message, title, timeout);
    }

    public showWarning(message: string, title?: string, timeout?: number): ToastMessage {
        return this.createToastMessage('warning', message, title, timeout);
    }

    public dismissToast(toast: ToastMessage) {
        const currentToasts = this.toasts$.getValue();
        this.toasts$.next(currentToasts.filter((t) => t.id !== toast.id));
    }

    public getToasts$(): Observable<ToastMessage[]> {
        return this.toasts$.asObservable();
    }

    private createToastMessage(type: ToastType, message: string, title?: string | null, timeout?: number | null, clickHandler?: (() => void) | null) {
        const currentToasts = this.toasts$.getValue();

        const toastMessage: ToastMessage = {
            id: uuid(),
            type,
            message,
            clickHandler,
            title: title ?? DEFAULT_TOAST_TITLE,
            timeout: timeout ?? DEFAULT_TOAST_TIMEOUT_MS
        };

        setTimeout(() => this.dismissToast(toastMessage), toastMessage.timeout);

        const newToasts = [toastMessage, ...currentToasts];

        if (newToasts.length > MAX_TOASTS_COUNT) {
            newToasts.pop();
        }

        this.toasts$.next(newToasts);
        return toastMessage;
    }
}