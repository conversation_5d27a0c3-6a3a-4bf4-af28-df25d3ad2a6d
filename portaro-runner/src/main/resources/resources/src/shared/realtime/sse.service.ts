import type {Observable} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import type CurrentAuthService from 'shared/services/current-auth.service';
import {exists} from 'shared/utils/custom-utils';
import type {NetworkInfoService} from 'shared/network-info/network-info.service';
import type {LogService} from 'core/logging/log.service';

export interface SseEventUnsubscriber {
    unsubscribe(): void;
}

interface StoredListener {
    id: string;
    eventName: string;
    listener: (event: MessageEvent) => void;
}

export const SseConnectionStates = {
    DISABLED: 'DISABLED',
    UNINITIALIZED: 'UNINITIALIZED',
    CONNECTING: 'CONNECTING',
    CONNECTED: 'CONNECTED',
    RECONNECTING: 'RECONNECTING',
    CLOSED: 'CLOSED'
} as const;

export interface ConnectionState {
    state: typeof SseConnectionStates[keyof typeof SseConnectionStates];
}

export class SseService {
    public static serviceName = 'sseService';

    private static SSE_ROUTE = '/sse-stream';

    private eventSource: EventSource | null = null;
    private connectionState: BehaviorSubject<ConnectionState>;
    private storedListeners: StoredListener[] = [];
    private listenerIdCounter = 0;

    /*@ngInject*/
    constructor(private currentAuthService: CurrentAuthService, private networkInfoService: NetworkInfoService, private logService: LogService) {
        this.connectionState = new BehaviorSubject({state: SseConnectionStates.UNINITIALIZED});
    }

    public setup(): void {
        if (this.networkInfoService.isHttp1x()) {
            this.logService.warn('SSE is disabled because the connection uses an outdated HTTP version (HTTP/1.x).')
            this.connectionState.next({state: SseConnectionStates.DISABLED});
            return;
        }

        if (this.currentAuthService.getCurrentAuthValue().evided) {
            this.setupSseStream();
        }

        this.currentAuthService.currentAuth$().subscribe((auth) => {
            if (auth.evided) {
                if (exists(this.eventSource)) {
                    this.closeSseStream();
                }

                this.setupSseStream();
            }

            if (!auth.evided && exists(this.eventSource)) {
                this.closeSseStream();
            }
        });
    }

    public addMessageListener<T>(eventName: string, listener: (data: T) => void): SseEventUnsubscriber {
        const listenerFunc = (event: MessageEvent) => {
            listener(JSON.parse(event.data));
        };

        const listenerId = `listener_${++this.listenerIdCounter}`;
        const storedListener: StoredListener = {
            id: listenerId,
            eventName,
            listener: listenerFunc
        };

        this.storedListeners.push(storedListener);

        if (exists(this.eventSource)) {
            this.eventSource.addEventListener(eventName, listenerFunc);
        }

        return {
            unsubscribe: () => {
                this.storedListeners = this.storedListeners.filter((l) => l.id !== listenerId);

                if (exists(this.eventSource)) {
                    this.eventSource.removeEventListener(eventName, listenerFunc);
                }
            }
        };
    }

    public setupSseStream(): void {
        this.connectionState.next({state: SseConnectionStates.CONNECTING});

        this.eventSource = new EventSource(SseService.SSE_ROUTE, {
            withCredentials: true
        });

        this.eventSource.addEventListener('open', () => {
            this.connectionState.next({state: SseConnectionStates.CONNECTED});
        });

        this.eventSource.addEventListener('error', () => {
            if (this.eventSource.readyState === EventSource.CLOSED) {
                this.connectionState.next({state: SseConnectionStates.CLOSED});
            } else if (this.eventSource.readyState === EventSource.CONNECTING) {
                this.connectionState.next({state: SseConnectionStates.RECONNECTING});
            }
        });

        this.reapplyStoredListeners();
    }

    public get connectionState$(): Observable<ConnectionState> {
        return this.connectionState.asObservable();
    }

    private closeSseStream(): void {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
            this.connectionState.next({state: SseConnectionStates.CLOSED});
        }
    }

    private reapplyStoredListeners(): void {
        if (!exists(this.eventSource)) {
            return;
        }

        for (const storedListener of this.storedListeners) {
            this.eventSource.addEventListener(storedListener.eventName, storedListener.listener);
        }
    }
}