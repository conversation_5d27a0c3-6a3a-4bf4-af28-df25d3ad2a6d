<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {onDestroy} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import ThreadIcon from 'src/features/threads/shared-components/ThreadIcon.svelte';

    export let thread: Thread;
    export let selected = false;

    const threadContextService = getInjector().getByClass(ThreadsContextService);

    let notificationsCount = 0;
    const notificationsCountSubscription = threadContextService.context$.subscribe((context) => {
        notificationsCount = context.unreadMessages.get(thread.id) ?? 0;
    });

    onDestroy(() => {
        unsubscribeAllSubscriptions(notificationsCountSubscription);
    });
</script>

<button class="thread-chip-button"
        class:thread-selected="{selected}"
        class:has-notifications="{!selected && notificationsCount > 0}"
        on:click>

    <ThreadIcon {thread} sizePx="{14}" tooltipsDisabled/>
    {thread.name}

    {#if notificationsCount > 0}
        <span class="notifications-count">{notificationsCount}</span>
    {/if}
</button>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @chip-height: 22px;

    .thread-chip-button {
        background-color: transparent;
        outline: none;
        cursor: pointer;
        height: @chip-height;
        padding: 0 @spacing-sm;
        border-radius: calc(@chip-height / 2);
        border: 1px solid @themed-border-default;
        gap: @spacing-s;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: @font-size-small;
        transition: opacity 0.3s ease-in-out;

        &.thread-selected {
            background-color: var(--accent-blue-new);
            border-color: rgba(0, 0, 0, 0.2);
            color: white;
        }

        &.has-notifications {
            border-color: var(--danger-red);
        }

        .notifications-count {
            color: var(--danger-red);
            font-weight: 500;
            font-size: @font-size-xs;
        }

        &:hover {
            opacity: 0.75;
        }
    }
</style>