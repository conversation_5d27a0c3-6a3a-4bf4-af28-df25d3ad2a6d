<script lang="ts">
    import type {Auth, Message} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {getDateFormatter, getInjector} from 'core/svelte-context/context';
    import {onDestroy} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';

    export let message: Message;
    export let withoutHeader = false;

    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const dateFormatter = getDateFormatter();

    let currentAuth: Auth;
    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((auth) => currentAuth = auth);

    $: selfMessage = message.senderUser.id === currentAuth?.activeUser?.id;

    onDestroy(() => {
        unsubscribeAllSubscriptions(currentAuthSubscription);
    });
</script>

<div class="thread-message-bubble" class:with-header="{!withoutHeader}">
    {#if !selfMessage}
        <div class="user-avatar-container" class:without-header="{withoutHeader}">
            <KpUserAvatar user="{message.senderUser}" sizePx="{42}"/>
        </div>
    {/if}

    {#if selfMessage}
        <span class="sent-at-date">
            {pipe(message.creationEvent.createDate, dateFormatter('dd.MM.yyyy HH:mm'))}
        </span>
    {/if}

    <div class="user-name-and-message-container">
        {#if !selfMessage && !withoutHeader}
            <span class="user-name">{message.senderUser.text}</span>
        {/if}

        <div class="message"
             class:self-message="{selfMessage}"
             class:other-message="{!selfMessage}">

            {#if exists(message.content)}
                {message.content}
            {/if}

            <!-- TODO: Show images/attachments
            {#if exists(message.imageUrl)}
                <img class="message-image" src="{message.imageUrl}" alt=""/>
            {/if}-->
        </div>
    </div>

    {#if !selfMessage}
        <span class="sent-at-date">
            {pipe(message.creationEvent.createDate, dateFormatter('dd.MM.yyyy HH:mm'))}
        </span>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    @self-message-color: var(--accent-blue-new);
    @self-message-text-color: white;

    @other-message-color: #777777;
    @other-message-text-color: #FFFFFF;

    .thread-message-bubble {
        max-width: 460px;
        display: flex;
        align-items: flex-start;
        gap: @spacing-ml;

        &.with-header {
            margin-top: @spacing-m;
        }

        .user-avatar-container.without-header {
            opacity: 0;
        }

        .user-name-and-message-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;

            .user-name {
                font-weight: 500;
                white-space: nowrap;
            }

            .message {
                flex: 1 1 0;
                display: flex;
                flex-direction: column;
                padding: @spacing-sm;
                gap: @spacing-sm;

                /* TODO: Show message images
                .message-image {
                    width: 100%;
                    height: auto;
                }
                */

                &.self-message {
                    background-color: @self-message-color;
                    color: @self-message-text-color;
                    border-radius: @border-radius-xl 0 @border-radius-xl @border-radius-xl;

                    /*
                    .message-image {
                        border-radius: @border-radius-large 0 @border-radius-large @border-radius-large;
                    }
                    */
                }

                &.other-message {
                    background-color: @other-message-color;
                    color: @other-message-text-color;
                    border-radius: 0 @border-radius-xl @border-radius-xl @border-radius-xl;

                    /*
                    .message-image {
                        border-radius: 0 @border-radius-large @border-radius-large @border-radius-large;
                    }
                    */
                }
            }
        }

        .sent-at-date {
            align-self: flex-end;
            font-size: 10px;
            color: @themed-text-muted;
            white-space: nowrap;
        }
    }
</style>