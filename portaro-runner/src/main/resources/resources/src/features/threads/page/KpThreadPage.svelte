<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import {getInjector} from 'core/svelte-context/context';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';
    import ThreadIcon from 'src/features/threads/shared-components/ThreadIcon.svelte';
    import ThreadContent from 'src/features/threads/thread-content/ThreadContent.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const threadContextService = getInjector().getByClass(ThreadsContextService);

    export let thread: Thread;
</script>

<ErpPageLayout pageClass="kp-message-thread-page" withoutContentPadding>
    <ErpHeadingBar slot="heading" gap="16px">
        <ThreadIcon {thread} sizePx="{48}"/>
        <KpHeading type="h1">{thread.name}</KpHeading>
    </ErpHeadingBar>

    <div class="thread-content-container">
        <ThreadContent {thread} placement="fullscreen-page"/>
    </div>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .thread-content-container {
        .flex-grow();
        gap: @spacing-ml;
    }

    :global {
        .kp-message-thread-page-container .erp-heading {
            padding: @spacing-ml @main-padding-horizontal !important;
        }
    }
</style>