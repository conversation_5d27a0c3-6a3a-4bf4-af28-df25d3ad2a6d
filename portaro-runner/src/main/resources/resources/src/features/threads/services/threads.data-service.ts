import type {Message, UUID} from 'typings/portaro.be.types';
import type {UserId} from 'src/features/user/types';
import type {AjaxService} from 'core/data-services/ajax.service';
import {ngAsync} from 'shared/utils/ng-@decorators';
import {transferify} from 'shared/utils/data-service-utils';
import type {
    AddThreadParticipantsRequest,
    NewThreadMessageRequest,
    NewThreadRequest,
    PublishMessageRequest,
    RemoveThreadParticipantsRequest,
    RichThread,
    Thread,
    UpdateThreadRequest
} from '../threads.types';

export class ThreadsDataService {
    public static readonly serviceName = 'threadsDataService';

    public static readonly THREADS_ROUTE = 'threads';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async createNewThreadMessage(message: NewThreadMessageRequest): Promise<Message> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/send`)
            .post(transferify(message));
    }

    @ngAsync()
    public async publishMessage(message: PublishMessageRequest): Promise<Message> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/publish`)
            .post(transferify(message));
    }

    @ngAsync()
    public async createNewThread(thread: NewThreadRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/create`)
            .post(transferify(thread));
    }

    @ngAsync()
    public async getThreadById(id: UUID): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/${id}`)
            .get();
    }

    @ngAsync()
    public async getThreadByRecordId(recordId: UUID): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/record/${recordId}`)
            .get();
    }

    @ngAsync()
    public async getUserThreads(userId: UserId): Promise<RichThread[]> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/user/${userId}`)
            .get();
    }

    @ngAsync()
    public async addThreadParticipants(request: AddThreadParticipantsRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/add-participants`)
            .post(transferify(request));
    }

    @ngAsync()
    public async removeThreadParticipants(request: RemoveThreadParticipantsRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/remove-participants`)
            .post(transferify(request));
    }

    @ngAsync()
    public async updateThread(request: UpdateThreadRequest): Promise<Thread> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/update`)
            .post(transferify(request));
    }

    @ngAsync()
    public async userReadAllThreadMessages(thread: Thread, userId: UserId): Promise<void> {
        return this.ajaxService
            .createRequest(`${ThreadsDataService.THREADS_ROUTE}/read-all-messages`)
            .post(transferify({thread, user: userId}));
    }
}