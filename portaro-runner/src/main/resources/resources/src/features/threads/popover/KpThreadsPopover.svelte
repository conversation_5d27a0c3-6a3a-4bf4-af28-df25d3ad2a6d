<script lang="ts">
    import type {Thread} from 'src/features/threads/threads.types';
    import type {UUID} from 'typings/portaro.be.types';
    import type {ThreadsContext} from 'src/features/threads/services/threads-context.service';
    import {getInjector} from 'core/svelte-context/context';
    import {ThreadsContextService} from 'src/features/threads/services/threads-context.service';
    import {exists, ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import {onDestroy, onMount} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {OPEN_THREAD_POPOVER_EVENT} from 'src/features/threads/threads.constants';
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';
    import ThreadContent from 'src/features/threads/thread-content/ThreadContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import ThreadsList from 'src/features/threads/popover/components/ThreadsList.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import NewThreadPopoverPage from 'src/features/threads/popover/components/new-thread/NewThreadPopoverPage.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import ThreadPopoverHeader from 'src/features/threads/popover/components/ThreadPopoverHeader.svelte';

    const contextService = getInjector().getByClass(ThreadsContextService);

    let threadContext = contextService.getContextValue();
    const threadContextSubscription = contextService.context$.subscribe((newContext) => threadContext = newContext);

    let page: 'main' | 'thread-content' | 'new-thread' = 'main';
    let animationDirection = 0;
    let openedThread: Thread | null = null;
    $: selectedThread = getSelectedThread(threadContext, openedThread);

    onMount(() => {
        contextService.eventBus.addEventListener(OPEN_THREAD_POPOVER_EVENT, handleOpenThreadPopover);
    });

    onDestroy(() => {
        unsubscribeAllSubscriptions(threadContextSubscription);
        contextService.eventBus.removeEventListener(OPEN_THREAD_POPOVER_EVENT, handleOpenThreadPopover);
    });

    function getSelectedThread(context: ThreadsContext, thread: Thread | null): Thread | null {
        if (!exists(thread)) {
            return null;
        }

        return context.threads.find((t) => t.id === thread.id) ?? thread;
    }

    const handleThreadSelectedOrCreated = (event: CustomEvent<Thread>) => {
        openedThread = event.detail;
        animationDirection = 0;
        page = 'thread-content';
    };

    const handleGoToNewThreadPage = () => {
        animationDirection = 1;
        openedThread = null;
        page = 'new-thread';
    };

    const handleGoToMainPage = () => {
        animationDirection = page === 'thread-content' ? 0 : -1;
        openedThread = null;
        page = 'main';
    };

    const handlePopoverClose = () => {
        setTimeout(() => {
            page = 'main';
            openedThread = null;
        }, 250); // Account for the animation duration
    };

    const handleOpenThreadPopover = (event: CustomEvent<UUID>) => {
        const foundThread = threadContext.threads.find((t) => t.id === event.detail);
        if (!exists(foundThread)) {
            return;
        }

        // OPEN

        openedThread = foundThread;
        page = 'thread-content';
    };
</script>

<KpPopover buttonSize="md"
           placement="bottom-end"
           buttonStyle="no-background"
           additionalPopoverPanelClasses="kp-threads-popover-panel"
           additionalPopoverButtonClasses="kp-threads-popover-button"
           on:popover-close={handlePopoverClose}>

    <svelte:fragment slot="button">
        <UIcon icon="messages"/>
        <span class="total-unread-threads" class:unread={threadContext.unreadThreadsCount > 0}>{threadContext.unreadThreadsCount}</span>
    </svelte:fragment>

    <div slot="popover-content" class="kp-threads-popover-content">
        {#if !threadContext.initialized}
            <div class="centered-container">
                <KpLoadingBlock size="sm"/>
            </div>
        {/if}

        {#if threadContext.initialized}
            {#key page}
                <div class="anim-container" in:fly={{x: 15 * animationDirection, duration: 250}}>
                    {#if threadContext.loadError}
                        <div class="centered-container">
                            <IconedContent icon="exclamation"
                                           iconColor="var(--danger-red)"
                                           orientation="vertical"
                                           align="center"
                                           justify="center">

                                Nastala chyba při načítání!
                            </IconedContent>
                        </div>
                    {:else}
                        <ThreadsList {selectedThread}
                                     on:new-thread={handleGoToNewThreadPage}
                                     on:thread-select={handleThreadSelectedOrCreated}/>

                        {#if page === 'main' || page === 'thread-content'}
                            {#if !exists(selectedThread)}
                                <div class="centered-container">
                                    <IconedContent gap="0px"
                                                   icon="messages"
                                                   orientation="vertical"
                                                   align="center"
                                                   justify="center">

                                        Vyberte nebo vytvořte konverzaci
                                    </IconedContent>
                                </div>
                            {/if}

                            {#if exists(openedThread)}
                                {#key openedThread}
                                    <div class="anim-container" in:fly={{y: 10, duration: 250}}>
                                        <ThreadPopoverHeader on:close={handleGoToMainPage} thread="{selectedThread}"/>
                                        <ThreadContent placement="popover" thread="{selectedThread}"/>
                                    </div>
                                {/key}
                            {/if}
                        {/if}

                        {#if page === 'new-thread'}
                            <NewThreadPopoverPage on:back={handleGoToMainPage}
                                                  on:thread-created={handleThreadSelectedOrCreated}/>
                        {/if}
                    {/if}
                </div>
            {/key}
        {/if}
    </div>
</KpPopover>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .total-unread-threads {
        font-weight: 500;
        font-size: @font-size-small;

        &.unread {
            color: var(--danger-red);
        }
    }

    .kp-threads-popover-content {
        padding: @spacing-s 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        gap: @spacing-m;

        .anim-container {
            .flex-grow();
        }

        .centered-container {
            .flex-grow();
            align-items: center;
            justify-content: center;
        }
    }

    :global {
        .kp-threads-popover-panel {
            --popover-bg-color: @themed-body-bg;
            --popover-border-radius: @border-radius-xl;
            --popover-border-color: @themed-border-default;
            --popover-content-padding: 0;

            width: 800px;
            height: 960px;
            max-width: 90vw;
            max-height: 90vh;
            margin-top: @spacing-s;

            .content {
                display: flex;
                flex-direction: column;
                height: 100%;

                .kp-threads-popover-content {
                    .flex-grow();
                    padding: 0;
                    gap: 0;
                }
            }

            .kp-button {
                outline: none;
            }
        }
    }
</style>