<script lang="ts">
    import type {TabButton} from 'shared/ui-widgets/tabset/types';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import ProjectDetailFinancialOverviewTab from './tab-pages/ProjectDetailFinancialOverviewTab.svelte';
    import ProjectDetailInfoTab from 'src/features/sutor/pages/project-detail/tab-pages/ProjectDetailInfoTab.svelte';
    import ProjectDetailDebugTab from 'src/features/sutor/pages/project-detail/tab-pages/ProjectDetailDebugTab.svelte';

    const tabButtons: TabButton[] = [
        {
            id: 'tab-info',
            label: 'Základní informace',
            icon: 'info'
        },
        {
            id: 'tab-debug',
            label: 'Debug',
            icon: 'bug'
        },
        {
            id: 'tab-reported-work',
            label: 'Hosp<PERSON><PERSON><PERSON><PERSON>',
            icon: 'edit'
        }
    ];

    const tabs = {
        'tab-info': {component: ProjectDetailInfoTab},
        'tab-reported-work': {component: ProjectDetailFinancialOverviewTab},
        'tab-debug': {component: ProjectDetailDebugTab}
    };
</script>

<ErpTabbedSubpagesContainer {tabButtons}
                            withoutUrlManagement
                            additionalClasses="sutor-project-detail"
                            let:activeTab>

    <svelte:component this="{tabs[activeTab].component}" {...tabs[activeTab].props ?? {}}/>
</ErpTabbedSubpagesContainer>