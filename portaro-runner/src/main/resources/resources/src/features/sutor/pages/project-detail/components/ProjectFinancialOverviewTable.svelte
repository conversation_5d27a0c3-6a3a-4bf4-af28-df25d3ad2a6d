<script lang="ts">
    import type {Document} from 'typings/portaro.be.types';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import FinancialOverviewProjectItem from 'src/features/sutor/pages/project-detail/components/FinancialOverviewProjectItem.svelte';

    export let projectRecord: Document;
</script>

<KpBarebonesTable additionalClasses="project-financial-overview-table"
                  fontSize="13px"
                  rowsTopBordered
                  colorAccented
                  headerFooterDivided>

    <tr slot="header">
        <th>Zakázka</th>
        <th></th>
        <th class="align-end">Práce</th>
        <th class="align-end">Stroj</th>
        <th class="align-end">Materiál</th>
        <th class="align-end">Subka</th>
        <th class="align-end">Ostatní</th>
        <th class="align-end">Náklady celkem</th>
        <th class="align-end">Výnos / Zisk</th>
    </tr>

    <svelte:fragment slot="body">
        <FinancialOverviewProjectItem index="{0}" {projectRecord}/>
    </svelte:fragment>
</KpBarebonesTable>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    :global {
        .project-financial-overview-table {
            th.align-end {
                text-align: end !important;
            }
        }
    }
</style>