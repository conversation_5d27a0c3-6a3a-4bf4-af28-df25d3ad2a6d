package cz.kpsys.portaro.auth.ezak;

import cz.kpsys.portaro.auth.AuthenticationException;
import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.StringValueCommand;
import cz.kpsys.portaro.record.edit.FieldEditationCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordFieldEditor;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.user.*;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.SourceOfData;
import cz.kpsys.portaro.user.edit.UserEditation;
import cz.kpsys.portaro.user.edit.UserEditationFactory;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.relation.UserRelation;
import cz.kpsys.portaro.user.relation.UserRelationLoader;
import cz.kpsys.portaro.user.relation.UserRelationsSaveCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Collections.emptyList;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class EzakAuthenticator implements Authenticator<EzakAuthenticationRequest, EzakSuccessAuthentication> {

    private static final FieldTypeId AUTH_F = FieldTypeId.top("a119");
    private static final FieldTypeId AUTH_NAME_SF = FieldTypeId.subfield(AUTH_F, "a");
    private static final FieldTypeId AUTH_ADDRESS_SF = FieldTypeId.subfield(AUTH_F, "b");
    private static final FieldTypeId AUTH_ICO_SF = FieldTypeId.subfield(AUTH_F, "7");
    private static final FieldTypeId AUTH_COUNTRY_SF = FieldTypeId.subfield(AUTH_F, "g");

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull UserRelationLoader userRelationLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> notCriticalDetailedAuthoritySearchSqlLoader;
    @NonNull Saver<UserRelationsSaveCommand, ?> userRelationsSaver;
    @NonNull UserEditationFactory userEditationFactory;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull Provider<@NonNull Fond> supplierAuthorityFondProvider;
    @NonNull SideThreadAuthenticationIsolator authIsolator;
    @NonNull PhoneNumberValidator phoneNumberValidator;


    @Override
    public EzakSuccessAuthentication authenticate(EzakAuthenticationRequest authRequest) throws org.springframework.security.core.AuthenticationException {
        Response response = authRequest.getResponse();
        Department ctx = authRequest.getDepartment();

        if (response.getStatus().equals(ResponseStatus.ERROR)) {
            throw new EzakErrorStatusResponseException(response.getMessage(), authRequest.getResponseXml());
        }

        User user = authIsolator.getAuthenticated(ctx, currentAuth -> getOrCreateUserAndSupplier(response, currentAuth, ctx));

        log.info("Supplier {} successfully loaded", user);

        return new EzakSuccessAuthentication(user, ctx);
    }


    private User getOrCreateUserAndSupplier(Response response, UserAuthentication authenticatorAuth, Department ctx) {
        EzakIdentity identity = response.getIdentity();
        try {
            validateResponseIdentity(response.getIdentity());
        } catch (RuntimeException e) {
            throw new AuthenticationException(String.format("Validation error while loading supplier from E-ZAK (response: %s): %s", response, e.getMessage()), Texts.ofNative("Validation error while loading supplier from E-ZAK: " + e.getMessage()), SeveritedException.SEVERITY_ERROR, e);
        }

        EzakCompany ezakCompany = identity.getCompany();
        EzakUser ezakUser = identity.getUser();


        //supplier company authority
        Record supplierAuthority;
        Optional<Record> existingSupplierAuthority = notCriticalDetailedAuthoritySearchSqlLoader.getMaxOne(StaticParamsModifier.ofNotEmpty(RecordConstants.SearchParams.ICO, ezakCompany.getIco()));
        if (existingSupplierAuthority.isPresent()) {
            log.info("Supplier (ičo '{}') found in database", ezakCompany.getIco());
            supplierAuthority = existingSupplierAuthority.get();
            checkAndSaveMissingFields(supplierAuthority, ezakCompany.getName(), ezakCompany.getIco(), ezakCompany.getAddress(), ezakCompany.getCountry(), authenticatorAuth, ctx);
        } else {
            log.info("No supplier with ičo '{}' in database, creating new", ezakCompany.getIco());
            supplierAuthority = createAndSaveNewSupplierAuthority(ezakCompany.getName(), ezakCompany.getIco(), ezakCompany.getAddress(), ezakCompany.getCountry(), authenticatorAuth, ctx);
        }

        //supplier company user
        Institution supplierUser = (Institution) (userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.RECORD_ID, List.of(supplierAuthority.getId()))).orElse(null));
        supplierUser = createOrFixAndSaveNewSupplierUser(authenticatorAuth, ctx, supplierUser, supplierAuthority, ezakCompany.getName(), ezakCompany.getIco(), ezakCompany.getAddress(), ezakCompany.getCountry(), ezakUser.getEmail(), ezakUser.getPhone().orElse(null));

        //supplier person user
        Person representer = (Person) (userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.EMAIL, List.of(ezakUser.getEmail()))).orElse(null));
        if (representer == null) {
            log.info("No supplier representer with email {} (in company ičo {}) in database, creating new", ezakUser.getEmail(), ezakCompany.getIco());
        } else {
            log.info("Supplier representer (id {} email {} in company ičo {}) found in database", representer.getId(), representer.getEmail(), ezakCompany.getIco());
        }
        representer = createOrFixAndSaveNewRepresenter(authenticatorAuth, ctx, representer, supplierUser, ezakUser.getFirstname(), ezakUser.getSurname(), ezakUser.getEmail(), ezakUser.getPhone().orElse(null));


        return representer;
    }



    private Institution createOrFixAndSaveNewSupplierUser(UserAuthentication currentExternalAuth, Department currentDepartment, @Nullable Institution existing, Record supplierCompanyAuthority, String name, String ico, String address, String country, String deprecatedRepresentedUserEmail, @Nullable String deprecatedRepresentedPersonPhone) {
        UserEditation<InstitutionEditationCommand, Institution> editation = Optional.ofNullable(existing)
                .map(institution -> userEditationFactory.<InstitutionEditationCommand, Institution>ofExistingUser(institution, currentDepartment, currentExternalAuth))
                .orElseGet(() -> {
                    InstitutionEditationCommand userEditationCommand = InstitutionEditationCommand.withSupplierAccount(supplierCompanyAuthority);
                    userEditationCommand.setActive(Optional.of(true));
                    return userEditationFactory.ofNewInstitution(currentDepartment, currentExternalAuth, InstitutionType.COMPANY).apply(userEditationCommand);
                });


        Institution institution = editation.user();

        if (institution.getReadableDepartments().isEmpty()) {
            editation.apply(command -> {
                command.setReadableDepartments(Optional.of(SimpleEditableList.of(currentDepartment, DepartmentEditationMode.MERGE_DUPLICATES_OR_APPEND)));
            });
        }

        if (institution.getSupplierAccounts().isEmpty()) {
            editation.modify(command -> Objects.requireNonNull(command.getSupplierAccounts()).map(lists -> lists.add(new SupplierAccountEditationCommand())), SourceOfData.EXTERNAL_EZAK);
        }

        if (!Objects.equals(institution.getRecordId(), supplierCompanyAuthority.getId())) {
            editation.apply(command -> command.setRecord(supplierCompanyAuthority));
        }

        if (!Objects.equals(name, institution.getName())) {
            editation.apply(command -> command.setInstitutionName(Optional.of(name)));
        }

        if (!Objects.equals(ico, institution.getIco())) {
            editation.apply(command -> command.setInstitutionIco(Optional.of(ico)));
        }

        //delete deprecated email if exists
        if (deprecatedRepresentedUserEmail != null) {
            editation.removeEmailWhenNotExistsByTypeAndValue(deprecatedRepresentedUserEmail);
        }

        //delete depgrecated sms if exists
        if (deprecatedRepresentedPersonPhone != null) {
            editation.removePhoneNumberWhenNotExistsByTypeAndValue(deprecatedRepresentedPersonPhone);
        }

        editation.saveIfModified();

        return editation.user();
    }


    private Record createAndSaveNewSupplierAuthority(String name, String ico, String address, String country, UserAuthentication currentExternalAuth, Department ctx) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx)
                .ofNew(supplierAuthorityFondProvider.get())
                .build(currentExternalAuth);

        fillMissingFields(recordEditation, name, ico, address, country);

        recordEditation.publish(ctx, currentExternalAuth);

        return recordEditation.getRecord();
    }


    private void checkAndSaveMissingFields(Record supplierAuthority, String name, String ico, String address, String country, UserAuthentication currentExternalAuth, Department ctx) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx)
                .ofExisting(supplierAuthority)
                .build(currentExternalAuth);

        fillMissingFields(recordEditation, name, ico, address, country);

        recordEditation.saveIfModified(ctx, currentExternalAuth);
    }


    private void fillMissingFields(RecordEditation editation, String name, String ico, String address, String country) {
        FieldEditationCommand nameCommand = FieldEditationCommand.of(editation.getRecord(), AUTH_NAME_SF.toFieldIdWithAllFirstIndices(), new StringValueCommand(name))
                .createMissingHierarchy();
        recordFieldEditor.editField(editation, nameCommand);

        FieldEditationCommand addressCommand = FieldEditationCommand.of(editation.getRecord(), AUTH_ADDRESS_SF.toFieldIdWithAllFirstIndices(), new StringValueCommand(address))
                .createMissingHierarchy();
        recordFieldEditor.editField(editation, addressCommand);

        FieldEditationCommand idoCommand = FieldEditationCommand.of(editation.getRecord(), AUTH_ICO_SF.toFieldIdWithAllFirstIndices(), new StringValueCommand(ico))
                .createMissingHierarchy();
        recordFieldEditor.editField(editation, idoCommand);

        FieldEditationCommand countryCommand = FieldEditationCommand.of(editation.getRecord(), AUTH_COUNTRY_SF.toFieldIdWithAllFirstIndices(), new StringValueCommand(country))
                .createMissingHierarchy();
        recordFieldEditor.editField(editation, countryCommand);
    }


    private Person createOrFixAndSaveNewRepresenter(UserAuthentication currentExternalAuth, Department currentDepartment, @Nullable Person existing, Institution supplier, String firstName, String lastName, String emailString, @Nullable String phoneNumber) {
        UserEditation<PersonEditationCommand, Person> editation = Optional.ofNullable(existing)
                .map(person -> userEditationFactory.<PersonEditationCommand, Person>ofExistingUser(person, currentDepartment, currentExternalAuth))
                .orElseGet(() -> {
                    PersonEditationCommand userEditationCommand = new PersonEditationCommand();
                    userEditationCommand.setActive(Optional.of(true));
                    return userEditationFactory.ofNewPerson(currentDepartment, currentExternalAuth).apply(userEditationCommand);
                });


        Person person = editation.user();

        //person now cannot contain supplier role
        if (!person.getSupplierAccounts().isEmpty()) {
            editation.modify(command -> Objects.requireNonNull(command.getSupplierAccounts()).ifPresent(List::clear), SourceOfData.EXTERNAL_EZAK);
        }

        if (!Objects.equals(firstName, person.getFirstName())) {
            editation.apply(command -> {
                command.setFirstName(Optional.of(firstName));
                command.setNameSource(Optional.of(SourceOfData.EXTERNAL_EZAK));
            });
        }

        if (!Objects.equals(lastName, person.getLastName())) {
            editation.apply(command -> {
                command.setLastName(Optional.of(lastName));
                command.setNameSource(Optional.of(SourceOfData.EXTERNAL_EZAK));
            });
        }

        if (emailString != null) {
            editation.addEmailWhenNotExistsByValue(Contact.createNewEmail(person.getId(), emailString, SourceOfData.EXTERNAL_EZAK));
        }

        if (phoneNumber != null) {
            String normalizedPhoneNumber = phoneNumberValidator.toE164(phoneNumber);
            editation.addPhoneNumberWhenNotExistsByTypeAndValue(Contact.createNewSmsPhone(person.getId(), normalizedPhoneNumber, SourceOfData.EXTERNAL_EZAK));
        }

        editation.saveIfModified();

        List<UserRelation> actualUserRelations = userRelationLoader.getAllByUser(person);
        List<UserRelation> expectedUserRelations = new ArrayList<>(actualUserRelations);
        expectedUserRelations = ListUtil.createWithAddedIfNotContains(expectedUserRelations, UserRelation.createRepresenterOf(person, supplier));
        expectedUserRelations = ListUtil.createWithAddedIfNotContains(expectedUserRelations, UserRelation.createMemberOf(person, supplier));
        if (actualUserRelations.size() != expectedUserRelations.size()) {
            userRelationsSaver.save(new UserRelationsSaveCommand(emptyList(), expectedUserRelations, currentDepartment));
        }

        return person;
    }


    private void validateResponseIdentity(EzakIdentity identity) {
        EzakCompany company = identity.getCompany();
        Assert.hasText(company.getRole(), "Mandatory company role (\"role\") in E-ZAK is missing");
        if (!company.getRole().equals("CONTRACTOR")) { //role musí byt CONTRACTOR
            throw new IllegalStateException("Login via E-ZAK is for suppliers only!");
        }
        Assert.hasText(company.getName(), "Mandatory company name (\"name\") in E-ZAK is missing");
        Assert.hasText(company.getIco(), "Mandatory ičo (\"ico\") in E-ZAK is missing");
        Assert.hasText(company.getAddress(), "Mandatory company address (\"address\") in E-ZAK is missing");

        EzakUser user = identity.getUser();
        Assert.hasText(user.getEmail(), "Mandatory email (\"email\") in E-ZAK is missing");
        Assert.hasText(user.getFirstname(), "Mandatory first name (\"firstname\") in E-ZAK is missing");
        Assert.hasText(user.getSurname(), "Mandatory last name (\"surname\") in E-ZAK is missing");
    }
}
